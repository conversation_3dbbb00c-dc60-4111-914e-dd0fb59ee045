{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>R<PERSON>, CheckCircle, Clock, ExternalLink, Github, Users } from 'lucide-react'\nimport Link from 'next/link'\nimport { useEffect, useState } from 'react'\n\nexport default function HomePage() {\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // Simulate loading time for better UX\n    const timer = setTimeout(() => setIsLoading(false), 500)\n    return () => clearTimeout(timer)\n  }, [])\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-100\">\n        <div className=\"text-center\">\n          <div className=\"spinner w-8 h-8 mx-auto mb-4\"></div>\n          <p className=\"text-secondary-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100\">\n      {/* Header */}\n      <header className=\"bg-white/80 backdrop-blur-sm border-b border-secondary-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n                <CheckCircle className=\"w-5 h-5 text-white\" />\n              </div>\n              <h1 className=\"text-xl font-bold text-secondary-900\">\n                {process.env.NEXT_PUBLIC_APP_NAME || 'Task Management'}\n              </h1>\n            </div>\n            <nav className=\"flex items-center space-x-4\">\n              <Link \n                href=\"/dashboard\" \n                className=\"btn-primary flex items-center space-x-2\"\n              >\n                <span>Get Started</span>\n                <ArrowRight className=\"w-4 h-4\" />\n              </Link>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-6xl font-bold text-secondary-900 mb-6 text-balance\">\n            Manage Your Tasks with\n            <span className=\"text-primary-600 block\">WSO2 Choreo</span>\n          </h2>\n          <p className=\"text-xl text-secondary-600 mb-8 max-w-3xl mx-auto text-balance\">\n            A comprehensive full-stack sample application demonstrating modern web development \n            with Next.js frontend and Node.js backend, all deployed on WSO2 Choreo platform.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/dashboard\" className=\"btn-primary text-lg px-8 py-3\">\n              Start Managing Tasks\n            </Link>\n            <a \n              href=\"https://github.com/your-org/choreo-fullstack-sample\" \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              className=\"btn-outline text-lg px-8 py-3 flex items-center justify-center space-x-2\"\n            >\n              <Github className=\"w-5 h-5\" />\n              <span>View Source</span>\n              <ExternalLink className=\"w-4 h-4\" />\n            </a>\n          </div>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid md:grid-cols-3 gap-8 mb-16\">\n          <div className=\"card p-8 text-center hover:shadow-medium transition-shadow duration-300\">\n            <div className=\"w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-4\">\n              <CheckCircle className=\"w-6 h-6 text-primary-600\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-secondary-900 mb-3\">\n              Task Management\n            </h3>\n            <p className=\"text-secondary-600\">\n              Create, update, and organize your tasks with an intuitive interface. \n              Set priorities, due dates, and track progress effortlessly.\n            </p>\n          </div>\n\n          <div className=\"card p-8 text-center hover:shadow-medium transition-shadow duration-300\">\n            <div className=\"w-12 h-12 bg-success-100 rounded-xl flex items-center justify-center mx-auto mb-4\">\n              <Users className=\"w-6 h-6 text-success-600\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-secondary-900 mb-3\">\n              User Authentication\n            </h3>\n            <p className=\"text-secondary-600\">\n              Secure authentication powered by Choreo&apos;s managed authentication system.\n              No complex setup required - just focus on your application logic.\n            </p>\n          </div>\n\n          <div className=\"card p-8 text-center hover:shadow-medium transition-shadow duration-300\">\n            <div className=\"w-12 h-12 bg-warning-100 rounded-xl flex items-center justify-center mx-auto mb-4\">\n              <Clock className=\"w-6 h-6 text-warning-600\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-secondary-900 mb-3\">\n              Real-time Updates\n            </h3>\n            <p className=\"text-secondary-600\">\n              Experience seamless real-time updates with optimistic UI patterns \n              and efficient API communication between frontend and backend.\n            </p>\n          </div>\n        </div>\n\n        {/* Technology Stack */}\n        <div className=\"card p-8 mb-16\">\n          <h3 className=\"text-2xl font-bold text-secondary-900 mb-6 text-center\">\n            Built with Modern Technologies\n          </h3>\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            <div>\n              <h4 className=\"text-lg font-semibold text-secondary-900 mb-4\">Frontend</h4>\n              <ul className=\"space-y-2 text-secondary-600\">\n                <li className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-4 h-4 text-success-600\" />\n                  <span>Next.js 14 with App Router</span>\n                </li>\n                <li className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-4 h-4 text-success-600\" />\n                  <span>TypeScript for type safety</span>\n                </li>\n                <li className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-4 h-4 text-success-600\" />\n                  <span>Tailwind CSS for styling</span>\n                </li>\n                <li className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-4 h-4 text-success-600\" />\n                  <span>Choreo managed authentication</span>\n                </li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"text-lg font-semibold text-secondary-900 mb-4\">Backend</h4>\n              <ul className=\"space-y-2 text-secondary-600\">\n                <li className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-4 h-4 text-success-600\" />\n                  <span>Node.js with Express.js</span>\n                </li>\n                <li className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-4 h-4 text-success-600\" />\n                  <span>RESTful API with OpenAPI spec</span>\n                </li>\n                <li className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-4 h-4 text-success-600\" />\n                  <span>JWT authentication integration</span>\n                </li>\n                <li className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-4 h-4 text-success-600\" />\n                  <span>Health monitoring endpoints</span>\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"text-center\">\n          <h3 className=\"text-2xl font-bold text-secondary-900 mb-4\">\n            Ready to Get Started?\n          </h3>\n          <p className=\"text-lg text-secondary-600 mb-8 max-w-2xl mx-auto\">\n            Experience the power of full-stack development on WSO2 Choreo. \n            Sign in to start managing your tasks and explore the application features.\n          </p>\n          <Link href=\"/dashboard\" className=\"btn-primary text-lg px-8 py-3 inline-flex items-center space-x-2\">\n            <span>Launch Application</span>\n            <ArrowRight className=\"w-5 h-5\" />\n          </Link>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-secondary-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center text-secondary-600\">\n            <p>\n              Built with ❤️ for the WSO2 Choreo community. \n              <a \n                href=\"https://wso2.com/choreo/\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"text-primary-600 hover:text-primary-700 ml-1\"\n              >\n                Learn more about Choreo\n              </a>\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sCAAsC;QACtC,MAAM,QAAQ,WAAW,IAAM,aAAa,QAAQ;QACpD,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC;wCAAG,WAAU;kDACX,uDAAoC;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAsE;kDAElF,8OAAC;wCAAK,WAAU;kDAAyB;;;;;;;;;;;;0CAE3C,8OAAC;gCAAE,WAAU;0CAAiE;;;;;;0CAI9E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAgC;;;;;;kDAGlE,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;0DACN,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAG9D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAMpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAG9D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAMpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAG9D,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;;;;;;;kCAQtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgD;;;;;;0DAC9D,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAIZ,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgD;;;;;;0DAC9D,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAG3D,8OAAC;gCAAE,WAAU;0CAAoD;;;;;;0CAIjE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;;kDAChC,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;gCAAE;8CAED,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}