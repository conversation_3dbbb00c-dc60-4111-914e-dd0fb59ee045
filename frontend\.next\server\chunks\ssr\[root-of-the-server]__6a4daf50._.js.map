{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata, Viewport } from 'next'\nimport { Inter } from 'next/font/google'\nimport './globals.css'\n\nconst inter = Inter({ subsets: ['latin'] })\n\nexport const metadata: Metadata = {\n  title: 'Task Management - Choreo Full-Stack Sample',\n  description: 'A comprehensive task management application built with Next.js and deployed on WSO2 Choreo',\n  keywords: ['task management', 'choreo', 'nextjs', 'wso2', 'productivity'],\n  authors: [{ name: 'WSO2 Choreo Team' }],\n  robots: 'index, follow',\n  openGraph: {\n    title: 'Task Management - Choreo Full-Stack Sample',\n    description: 'A comprehensive task management application built with Next.js and deployed on WSO2 Choreo',\n    type: 'website',\n    locale: 'en_US',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: 'Task Management - Choreo Full-Stack Sample',\n    description: 'A comprehensive task management application built with Next.js and deployed on WSO2 Choreo',\n  },\n}\n\nexport const viewport: Viewport = {\n  width: 'device-width',\n  initialScale: 1,\n}\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <html lang=\"en\" className=\"h-full\">\n      <body className={`${inter.className} h-full`}>\n        <div id=\"root\" className=\"h-full\">\n          {children}\n        </div>\n      </body>\n    </html>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAMO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAmB;QAAU;QAAU;QAAQ;KAAe;IACzE,SAAS;QAAC;YAAE,MAAM;QAAmB;KAAE;IACvC,QAAQ;IACR,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,cAAc;AAChB;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;kBACxB,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,SAAS,CAAC,OAAO,CAAC;sBAC1C,cAAA,8OAAC;gBAAI,IAAG;gBAAO,WAAU;0BACtB;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}