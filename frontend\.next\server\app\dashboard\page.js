(()=>{var e={};e.id=105,e.ids=[105],e.modules={559:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Choreo\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Choreo\\frontend\\src\\app\\dashboard\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},971:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},1135:()=>{},3017:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3097:(e,t,r)=>{Promise.resolve().then(r.bind(r,559))},3144:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Choreo\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Choreo\\frontend\\src\\app\\dashboard\\layout.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3689:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(8962).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3823:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(687);function a({size:e="medium",className:t="",text:r}){return(0,s.jsxs)("div",{className:`flex flex-col items-center justify-center ${t}`,children:[(0,s.jsx)("div",{className:`spinner ${{small:"w-4 h-4",medium:"w-6 h-6",large:"w-8 h-8"}[e]}`}),r&&(0,s.jsx)("p",{className:"mt-2 text-sm text-secondary-600",children:r})]})}},3851:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(8962).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3873:e=>{"use strict";e.exports=require("path")},3902:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(687),a=r(3210),i=r(6189);r(4604);var n=r(3823);function l({children:e}){let[t,r]=(0,a.useState)(!0),[l,o]=(0,a.useState)(!1);return((0,i.useRouter)(),t)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-secondary-50",children:(0,s.jsx)(n.A,{size:"large",text:"Checking authentication..."})}):l?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-secondary-50",children:(0,s.jsx)(n.A,{size:"large",text:"Redirecting to login..."})})}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>n,viewport:()=>l});var s=r(7413),a=r(5041),i=r.n(a);r(1135);let n={title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",keywords:["task management","choreo","nextjs","wso2","productivity"],authors:[{name:"WSO2 Choreo Team"}],robots:"index, follow",openGraph:{title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo"}},l={width:"device-width",initialScale:1};function o({children:e}){return(0,s.jsx)("html",{lang:"en",className:"h-full",children:(0,s.jsx)("body",{className:`${i().className} h-full`,children:(0,s.jsx)("div",{id:"root",className:"h-full",children:e})})})}},4604:(e,t,r)=>{"use strict";function s(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)e[s]=r[s]}return e}r.d(t,{y1:()=>n,en:()=>l});var a=function e(t,r){function a(e,a,i){if("undefined"!=typeof document){"number"==typeof(i=s({},r,i)).expires&&(i.expires=new Date(Date.now()+864e5*i.expires)),i.expires&&(i.expires=i.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var n="";for(var l in i)i[l]&&(n+="; "+l,!0!==i[l]&&(n+="="+i[l].split(";")[0]));return document.cookie=e+"="+t.write(a,e)+n}}return Object.create({set:a,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var r=document.cookie?document.cookie.split("; "):[],s={},a=0;a<r.length;a++){var i=r[a].split("="),n=i.slice(1).join("=");try{var l=decodeURIComponent(i[0]);if(s[l]=t.read(n,l),e===l)break}catch(e){}}return e?s[e]:s}},remove:function(e,t){a(e,"",s({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,s({},this.attributes,t))},withConverter:function(t){return e(s({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});class i{constructor(){this.user=null,this.isAuthenticated=!1}static getInstance(){return i.instance||(i.instance=new i),i.instance}login(e){let t="/auth/login",r=e?`${t}?redirect=${encodeURIComponent(e)}`:t;window.location.href=r}logout(){let e=a.get("session_hint"),t=e?`/auth/logout?session_hint=${e}`:"/auth/logout";this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),window.location.href=t}getUserInfoFromCookie(){try{let e=a.get("userinfo");if(!e)return null;let t=JSON.parse(atob(e));return a.remove("userinfo",{path:"/"}),this.user={id:t.sub,email:t.email,name:t.name||`${t.given_name||""} ${t.family_name||""}`.trim(),username:t.preferred_username,groups:t.groups||[],roles:t.roles||[],profileComplete:!!(t.name&&t.email)},this.isAuthenticated=!0,this.storeUserData(this.user),this.user}catch(e){return console.error("Error parsing user info cookie:",e),null}}async checkAuthStatus(){try{let e=await fetch("/auth/userinfo",{method:"GET",credentials:"include",headers:{Accept:"application/json"}});if(e.ok){let t=await e.json();return this.user={id:t.sub,email:t.email,name:t.name||`${t.given_name||""} ${t.family_name||""}`.trim(),username:t.preferred_username,groups:t.groups||[],roles:t.roles||[],profileComplete:!!(t.name&&t.email)},this.isAuthenticated=!0,this.storeUserData(this.user),{isAuthenticated:!0,user:this.user}}if(401===e.status)return this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),{isAuthenticated:!1,user:null};throw Error(`Auth check failed with status: ${e.status}`)}catch(t){console.error("Error checking auth status:",t);let e=this.getStoredUserData();if(e)return this.user=e,this.isAuthenticated=!0,{isAuthenticated:!0,user:e};return{isAuthenticated:!1,user:null}}}getCurrentUser(){if(this.user)return this.user;let e=this.getStoredUserData();return e?(this.user=e,this.isAuthenticated=!0,e):null}getIsAuthenticated(){return this.isAuthenticated||!!this.getStoredUserData()}handleSessionExpiry(){console.log("Session expired, redirecting to login..."),this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),this.login(window.location.pathname)}storeUserData(e){try{localStorage.setItem("user",JSON.stringify(e)),localStorage.setItem("lastLogin",new Date().toISOString())}catch(e){console.error("Error storing user data:",e)}}getStoredUserData(){try{let e=localStorage.getItem("user");if(e)return JSON.parse(e)}catch(e){console.error("Error retrieving stored user data:",e)}return null}clearStoredUserData(){try{localStorage.removeItem("user"),localStorage.removeItem("lastLogin")}catch(e){console.error("Error clearing stored user data:",e)}}}let n=i.getInstance(),l=()=>n.handleSessionExpiry()},4625:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(5239),a=r(8088),i=r(8170),n=r.n(i),l=r(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,559)),"C:\\Choreo\\frontend\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,3144)),"C:\\Choreo\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Choreo\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Choreo\\frontend\\src\\app\\dashboard\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6020:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>e8});var s=r(687),a=r(3851),i=r(8962);let n=(0,i.A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function l({message:e,onDismiss:t,className:r="",variant:i="error"}){let l={error:"text-danger-600",warning:"text-warning-600",info:"text-primary-600"};return(0,s.jsx)("div",{className:`rounded-lg border p-4 ${{error:"bg-danger-50 border-danger-200 text-danger-800",warning:"bg-warning-50 border-warning-200 text-warning-800",info:"bg-primary-50 border-primary-200 text-primary-800"}[i]} ${r}`,children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(a.A,{className:`w-5 h-5 mt-0.5 mr-3 flex-shrink-0 ${l[i]}`}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("p",{className:"text-sm font-medium",children:e})}),t&&(0,s.jsx)("button",{onClick:t,className:`ml-3 flex-shrink-0 p-1 rounded-md hover:bg-opacity-20 hover:bg-current transition-colors duration-200 ${l[i]}`,children:(0,s.jsx)(n,{className:"w-4 h-4"})})]})})}let o=(0,i.A)("ArrowDownWideNarrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]]),d=(0,i.A)("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]),c=(0,i.A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);function u({filters:e,onFiltersChange:t,taskStats:r}){let a=(r,s)=>{let a={...e};""===s||void 0===s?delete a[r]:a[r]=s,t(a)},i=Object.keys(e).length>0;return(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("label",{htmlFor:"status-filter",className:"text-sm font-medium text-secondary-700",children:"Status:"}),(0,s.jsxs)("select",{id:"status-filter",value:e.status||"",onChange:e=>a("status",e.target.value),className:"text-sm border-secondary-300 rounded-md focus:border-primary-500 focus:ring-primary-500",children:[(0,s.jsxs)("option",{value:"",children:["All (",r.total,")"]}),(0,s.jsxs)("option",{value:"todo",children:["To Do (",r.byStatus.todo,")"]}),(0,s.jsxs)("option",{value:"in-progress",children:["In Progress (",r.byStatus["in-progress"],")"]}),(0,s.jsxs)("option",{value:"completed",children:["Completed (",r.byStatus.completed,")"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("label",{htmlFor:"priority-filter",className:"text-sm font-medium text-secondary-700",children:"Priority:"}),(0,s.jsxs)("select",{id:"priority-filter",value:e.priority||"",onChange:e=>a("priority",e.target.value),className:"text-sm border-secondary-300 rounded-md focus:border-primary-500 focus:ring-primary-500",children:[(0,s.jsx)("option",{value:"",children:"All"}),(0,s.jsxs)("option",{value:"high",children:["High (",r.byPriority.high,")"]}),(0,s.jsxs)("option",{value:"medium",children:["Medium (",r.byPriority.medium,")"]}),(0,s.jsxs)("option",{value:"low",children:["Low (",r.byPriority.low,")"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("label",{htmlFor:"sort-filter",className:"text-sm font-medium text-secondary-700",children:"Sort by:"}),(0,s.jsxs)("select",{id:"sort-filter",value:e.sortBy||"createdAt",onChange:e=>a("sortBy",e.target.value),className:"text-sm border-secondary-300 rounded-md focus:border-primary-500 focus:ring-primary-500",children:[(0,s.jsx)("option",{value:"createdAt",children:"Created Date"}),(0,s.jsx)("option",{value:"updatedAt",children:"Updated Date"}),(0,s.jsx)("option",{value:"title",children:"Title"}),(0,s.jsx)("option",{value:"priority",children:"Priority"}),(0,s.jsx)("option",{value:"dueDate",children:"Due Date"})]})]}),(0,s.jsxs)("button",{onClick:()=>a("sortOrder","desc"===e.sortOrder?"asc":"desc"),className:"flex items-center space-x-1 px-3 py-1 text-sm border border-secondary-300 rounded-md hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",title:`Sort ${"desc"===e.sortOrder?"ascending":"descending"}`,children:["desc"===e.sortOrder?(0,s.jsx)(o,{className:"w-4 h-4"}):(0,s.jsx)(d,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"desc"===e.sortOrder?"Desc":"Asc"})]}),i&&(0,s.jsxs)("button",{onClick:()=>{t({})},className:"flex items-center space-x-1 px-3 py-1 text-sm text-danger-600 border border-danger-300 rounded-md hover:bg-danger-50 focus:outline-none focus:ring-2 focus:ring-danger-500 focus:border-danger-500",children:[(0,s.jsx)(n,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Clear"})]}),i&&(0,s.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-secondary-600",children:[(0,s.jsx)(c,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[Object.keys(e).length," filter",1!==Object.keys(e).length?"s":""," active"]})]})]})}var m=r(3823);let h=(0,i.A)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);var f=r(8730);let x=(0,i.A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);var p=r(3210),y=e=>"checkbox"===e.type,g=e=>e instanceof Date,b=e=>null==e;let v=e=>"object"==typeof e;var j=e=>!b(e)&&!Array.isArray(e)&&v(e)&&!g(e),w=e=>j(e)&&e.target?y(e.target)?e.target.checked:e.target.value:e,k=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,N=(e,t)=>e.has(k(t)),A=e=>{let t=e.constructor&&e.constructor.prototype;return j(t)&&t.hasOwnProperty("isPrototypeOf")},S="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function C(e){let t,r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(!(!(S&&(e instanceof Blob||s))&&(r||j(e))))return e;else if(t=r?[]:{},r||A(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=C(e[r]));else t=e;return t}var _=e=>/^\w*$/.test(e),D=e=>void 0===e,V=e=>Array.isArray(e)?e.filter(Boolean):[],F=e=>V(e.replace(/["|']|\]/g,"").split(/\.|\[/)),T=(e,t,r)=>{if(!t||!j(e))return r;let s=(_(t)?[t]:F(t)).reduce((e,t)=>b(e)?e:e[t],e);return D(s)||s===e?D(e[t])?r:e[t]:s},O=e=>"boolean"==typeof e,P=(e,t,r)=>{let s=-1,a=_(t)?[t]:F(t),i=a.length,n=i-1;for(;++s<i;){let t=a[s],i=r;if(s!==n){let r=e[t];i=j(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};let E={BLUR:"blur",FOCUS_OUT:"focusout"},U={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},M={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},L=p.createContext(null);L.displayName="HookFormContext";var $=(e,t,r,s=!0)=>{let a={defaultValues:t._defaultValues};for(let i in e)Object.defineProperty(a,i,{get:()=>(t._proxyFormState[i]!==U.all&&(t._proxyFormState[i]=!s||U.all),r&&(r[i]=!0),e[i])});return a};let q="undefined"!=typeof window?p.useLayoutEffect:p.useEffect;var R=e=>"string"==typeof e,I=(e,t,r,s,a)=>R(e)?(s&&t.watch.add(e),T(r,e,a)):Array.isArray(e)?e.map(e=>(s&&t.watch.add(e),T(r,e))):(s&&(t.watchAll=!0),r),B=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},z=e=>Array.isArray(e)?e:[e],W=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},H=e=>b(e)||!v(e);function G(e,t,r=new WeakSet){if(H(e)||H(t))return e===t;if(g(e)&&g(t))return e.getTime()===t.getTime();let s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;if(r.has(e)||r.has(t))return!0;for(let i of(r.add(e),r.add(t),s)){let s=e[i];if(!a.includes(i))return!1;if("ref"!==i){let e=t[i];if(g(s)&&g(e)||j(s)&&j(e)||Array.isArray(s)&&Array.isArray(e)?!G(s,e,r):s!==e)return!1}}return!0}var J=e=>j(e)&&!Object.keys(e).length,Y=e=>"file"===e.type,Z=e=>"function"==typeof e,X=e=>{if(!S)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},K=e=>"select-multiple"===e.type,Q=e=>"radio"===e.type,ee=e=>Q(e)||y(e),et=e=>X(e)&&e.isConnected;function er(e,t){let r=Array.isArray(t)?t:_(t)?[t]:F(t),s=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,s=0;for(;s<r;)e=D(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(j(s)&&J(s)||Array.isArray(s)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!D(e[t]))return!1;return!0}(s))&&er(e,r.slice(0,-1)),e}var es=e=>{for(let t in e)if(Z(e[t]))return!0;return!1};function ea(e,t={}){let r=Array.isArray(e);if(j(e)||r)for(let r in e)Array.isArray(e[r])||j(e[r])&&!es(e[r])?(t[r]=Array.isArray(e[r])?[]:{},ea(e[r],t[r])):b(e[r])||(t[r]=!0);return t}var ei=(e,t)=>(function e(t,r,s){let a=Array.isArray(t);if(j(t)||a)for(let a in t)Array.isArray(t[a])||j(t[a])&&!es(t[a])?D(r)||H(s[a])?s[a]=Array.isArray(t[a])?ea(t[a],[]):{...ea(t[a])}:e(t[a],b(r)?{}:r[a],s[a]):s[a]=!G(t[a],r[a]);return s})(e,t,ea(t));let en={value:!1,isValid:!1},el={value:!0,isValid:!0};var eo=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!D(e[0].attributes.value)?D(e[0].value)||""===e[0].value?el:{value:e[0].value,isValid:!0}:el:en}return en},ed=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>D(e)?e:t?""===e?NaN:e?+e:e:r&&R(e)?new Date(e):s?s(e):e;let ec={isValid:!1,value:null};var eu=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,ec):ec;function em(e){let t=e.ref;return Y(t)?t.files:Q(t)?eu(e.refs).value:K(t)?[...t.selectedOptions].map(({value:e})=>e):y(t)?eo(e.refs).value:ed(D(t.value)?e.ref.value:t.value,e)}var eh=(e,t,r,s)=>{let a={};for(let r of e){let e=T(t,r);e&&P(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}},ef=e=>e instanceof RegExp,ex=e=>D(e)?e:ef(e)?e.source:j(e)?ef(e.value)?e.value.source:e.value:e,ep=e=>({isOnSubmit:!e||e===U.onSubmit,isOnBlur:e===U.onBlur,isOnChange:e===U.onChange,isOnAll:e===U.all,isOnTouch:e===U.onTouched});let ey="AsyncFunction";var eg=e=>!!e&&!!e.validate&&!!(Z(e.validate)&&e.validate.constructor.name===ey||j(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===ey)),eb=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),ev=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ej=(e,t,r,s)=>{for(let a of r||Object.keys(e)){let r=T(e,a);if(r){let{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;else if(e.ref&&t(e.ref,e.name)&&!s)return!0;else if(ej(i,t))break}else if(j(i)&&ej(i,t))break}}};function ew(e,t,r){let s=T(e,r);if(s||_(r))return{error:s,name:r};let a=r.split(".");for(;a.length;){let s=a.join("."),i=T(t,s),n=T(e,s);if(i&&!Array.isArray(i)&&r!==s)break;if(n&&n.type)return{name:s,error:n};if(n&&n.root&&n.root.type)return{name:`${s}.root`,error:n.root};a.pop()}return{name:r}}var ek=(e,t,r,s)=>{r(e);let{name:a,...i}=e;return J(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!s||U.all))},eN=(e,t,r)=>!e||!t||e===t||z(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),eA=(e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:(r?!s.isOnChange:!a.isOnChange)||e),eS=(e,t)=>!V(T(e,t)).length&&er(e,t),eC=(e,t,r)=>{let s=z(T(e,r));return P(s,"root",t[r]),P(e,r,s),e},e_=e=>R(e);function eD(e,t,r="validate"){if(e_(e)||Array.isArray(e)&&e.every(e_)||O(e)&&!e)return{type:r,message:e_(e)?e:"",ref:t}}var eV=e=>j(e)&&!ef(e)?e:{value:e,message:""},eF=async(e,t,r,s,a,i)=>{let{ref:n,refs:l,required:o,maxLength:d,minLength:c,min:u,max:m,pattern:h,validate:f,name:x,valueAsNumber:p,mount:g}=e._f,v=T(r,x);if(!g||t.has(x))return{};let w=l?l[0]:n,k=e=>{a&&w.reportValidity&&(w.setCustomValidity(O(e)?"":e||""),w.reportValidity())},N={},A=Q(n),S=y(n),C=(p||Y(n))&&D(n.value)&&D(v)||X(n)&&""===n.value||""===v||Array.isArray(v)&&!v.length,_=B.bind(null,x,s,N),V=(e,t,r,s=M.maxLength,a=M.minLength)=>{let i=e?t:r;N[x]={type:e?s:a,message:i,ref:n,..._(e?s:a,i)}};if(i?!Array.isArray(v)||!v.length:o&&(!(A||S)&&(C||b(v))||O(v)&&!v||S&&!eo(l).isValid||A&&!eu(l).isValid)){let{value:e,message:t}=e_(o)?{value:!!o,message:o}:eV(o);if(e&&(N[x]={type:M.required,message:t,ref:w,..._(M.required,t)},!s))return k(t),N}if(!C&&(!b(u)||!b(m))){let e,t,r=eV(m),a=eV(u);if(b(v)||isNaN(v)){let s=n.valueAsDate||new Date(v),i=e=>new Date(new Date().toDateString()+" "+e),l="time"==n.type,o="week"==n.type;R(r.value)&&v&&(e=l?i(v)>i(r.value):o?v>r.value:s>new Date(r.value)),R(a.value)&&v&&(t=l?i(v)<i(a.value):o?v<a.value:s<new Date(a.value))}else{let s=n.valueAsNumber||(v?+v:v);b(r.value)||(e=s>r.value),b(a.value)||(t=s<a.value)}if((e||t)&&(V(!!e,r.message,a.message,M.max,M.min),!s))return k(N[x].message),N}if((d||c)&&!C&&(R(v)||i&&Array.isArray(v))){let e=eV(d),t=eV(c),r=!b(e.value)&&v.length>+e.value,a=!b(t.value)&&v.length<+t.value;if((r||a)&&(V(r,e.message,t.message),!s))return k(N[x].message),N}if(h&&!C&&R(v)){let{value:e,message:t}=eV(h);if(ef(e)&&!v.match(e)&&(N[x]={type:M.pattern,message:t,ref:n,..._(M.pattern,t)},!s))return k(t),N}if(f){if(Z(f)){let e=eD(await f(v,r),w);if(e&&(N[x]={...e,..._(M.validate,e.message)},!s))return k(e.message),N}else if(j(f)){let e={};for(let t in f){if(!J(e)&&!s)break;let a=eD(await f[t](v,r),w,t);a&&(e={...a,..._(t,a.message)},k(a.message),s&&(N[x]=e))}if(!J(e)&&(N[x]={ref:w,...e},!s))return N}}return k(!0),N};let eT={mode:U.onSubmit,reValidateMode:U.onChange,shouldFocusError:!0};function eO({task:e,onSubmit:t,onCancel:r,isLoading:a=!1}){let[i,l]=(0,p.useState)(!1),o=!!e,{register:d,handleSubmit:c,formState:{errors:u},reset:m,watch:v}=function(e={}){let t=p.useRef(void 0),r=p.useRef(void 0),[s,a]=p.useState({isDirty:!1,isValidating:!1,isLoading:Z(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:Z(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:s},e.defaultValues&&!Z(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{let{formControl:r,...a}=function(e={}){let t,r={...eT,...e},s={submitCount:0,isDirty:!1,isReady:!1,isLoading:Z(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},a={},i=(j(r.defaultValues)||j(r.values))&&C(r.defaultValues||r.values)||{},n=r.shouldUnregister?{}:C(i),l={action:!1,mount:!1,watch:!1},o={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},d=0,c={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},u={...c},m={array:W(),state:W()},h=r.criteriaMode===U.all,f=e=>t=>{clearTimeout(d),d=setTimeout(e,t)},x=async e=>{if(!r.disabled&&(c.isValid||u.isValid||e)){let e=r.resolver?J((await F()).errors):await L(a,!0);e!==s.isValid&&m.state.next({isValid:e})}},p=(e,t)=>{!r.disabled&&(c.isValidating||c.validatingFields||u.isValidating||u.validatingFields)&&((e||Array.from(o.mount)).forEach(e=>{e&&(t?P(s.validatingFields,e,t):er(s.validatingFields,e))}),m.state.next({validatingFields:s.validatingFields,isValidating:!J(s.validatingFields)}))},v=(e,t)=>{P(s.errors,e,t),m.state.next({errors:s.errors})},k=(e,t,r,s)=>{let o=T(a,e);if(o){let a=T(n,e,D(r)?T(i,e):r);D(a)||s&&s.defaultChecked||t?P(n,e,t?a:em(o._f)):B(e,a),l.mount&&x()}},A=(e,t,a,n,l)=>{let o=!1,d=!1,h={name:e};if(!r.disabled){if(!a||n){(c.isDirty||u.isDirty)&&(d=s.isDirty,s.isDirty=h.isDirty=$(),o=d!==h.isDirty);let r=G(T(i,e),t);d=!!T(s.dirtyFields,e),r?er(s.dirtyFields,e):P(s.dirtyFields,e,!0),h.dirtyFields=s.dirtyFields,o=o||(c.dirtyFields||u.dirtyFields)&&!r!==d}if(a){let t=T(s.touchedFields,e);t||(P(s.touchedFields,e,a),h.touchedFields=s.touchedFields,o=o||(c.touchedFields||u.touchedFields)&&t!==a)}o&&l&&m.state.next(h)}return o?h:{}},_=(e,a,i,n)=>{let l=T(s.errors,e),o=(c.isValid||u.isValid)&&O(a)&&s.isValid!==a;if(r.delayError&&i?(t=f(()=>v(e,i)))(r.delayError):(clearTimeout(d),t=null,i?P(s.errors,e,i):er(s.errors,e)),(i?!G(l,i):l)||!J(n)||o){let t={...n,...o&&O(a)?{isValid:a}:{},errors:s.errors,name:e};s={...s,...t},m.state.next(t)}},F=async e=>{p(e,!0);let t=await r.resolver(n,r.context,eh(e||o.mount,a,r.criteriaMode,r.shouldUseNativeValidation));return p(e),t},M=async e=>{let{errors:t}=await F(e);if(e)for(let r of e){let e=T(t,r);e?P(s.errors,r,e):er(s.errors,r)}else s.errors=t;return t},L=async(e,t,a={valid:!0})=>{for(let i in e){let l=e[i];if(l){let{_f:e,...d}=l;if(e){let d=o.array.has(e.name),u=l._f&&eg(l._f);u&&c.validatingFields&&p([i],!0);let m=await eF(l,o.disabled,n,h,r.shouldUseNativeValidation&&!t,d);if(u&&c.validatingFields&&p([i]),m[e.name]&&(a.valid=!1,t))break;t||(T(m,e.name)?d?eC(s.errors,m,e.name):P(s.errors,e.name,m[e.name]):er(s.errors,e.name))}J(d)||await L(d,t,a)}}return a.valid},$=(e,t)=>!r.disabled&&(e&&t&&P(n,e,t),!G(el(),i)),q=(e,t,r)=>I(e,o,{...l.mount?n:D(t)?i:R(e)?{[e]:t}:t},r,t),B=(e,t,r={})=>{let s=T(a,e),i=t;if(s){let r=s._f;r&&(r.disabled||P(n,e,ed(t,r)),i=X(r.ref)&&b(t)?"":t,K(r.ref)?[...r.ref.options].forEach(e=>e.selected=i.includes(e.value)):r.refs?y(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(i)?e.checked=!!i.find(t=>t===e.value):e.checked=i===e.value||!!i)}):r.refs.forEach(e=>e.checked=e.value===i):Y(r.ref)?r.ref.value="":(r.ref.value=i,r.ref.type||m.state.next({name:e,values:C(n)})))}(r.shouldDirty||r.shouldTouch)&&A(e,i,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&en(e)},H=(e,t,r)=>{for(let s in t){if(!t.hasOwnProperty(s))return;let i=t[s],n=e+"."+s,l=T(a,n);(o.array.has(e)||j(i)||l&&!l._f)&&!g(i)?H(n,i,r):B(n,i,r)}},Q=(e,t,r={})=>{let d=T(a,e),h=o.array.has(e),f=C(t);P(n,e,f),h?(m.array.next({name:e,values:C(n)}),(c.isDirty||c.dirtyFields||u.isDirty||u.dirtyFields)&&r.shouldDirty&&m.state.next({name:e,dirtyFields:ei(i,n),isDirty:$(e,f)})):!d||d._f||b(f)?B(e,f,r):H(e,f,r),ev(e,o)&&m.state.next({...s}),m.state.next({name:l.mount?e:void 0,values:C(n)})},es=async e=>{l.mount=!0;let i=e.target,d=i.name,f=!0,y=T(a,d),b=e=>{f=Number.isNaN(e)||g(e)&&isNaN(e.getTime())||G(e,T(n,d,e))},v=ep(r.mode),j=ep(r.reValidateMode);if(y){let l,g,k=i.type?em(y._f):w(e),N=e.type===E.BLUR||e.type===E.FOCUS_OUT,S=!eb(y._f)&&!r.resolver&&!T(s.errors,d)&&!y._f.deps||eA(N,T(s.touchedFields,d),s.isSubmitted,j,v),D=ev(d,o,N);P(n,d,k),N?(y._f.onBlur&&y._f.onBlur(e),t&&t(0)):y._f.onChange&&y._f.onChange(e);let V=A(d,k,N),O=!J(V)||D;if(N||m.state.next({name:d,type:e.type,values:C(n)}),S)return(c.isValid||u.isValid)&&("onBlur"===r.mode?N&&x():N||x()),O&&m.state.next({name:d,...D?{}:V});if(!N&&D&&m.state.next({...s}),r.resolver){let{errors:e}=await F([d]);if(b(k),f){let t=ew(s.errors,a,d),r=ew(e,a,t.name||d);l=r.error,d=r.name,g=J(e)}}else p([d],!0),l=(await eF(y,o.disabled,n,h,r.shouldUseNativeValidation))[d],p([d]),b(k),f&&(l?g=!1:(c.isValid||u.isValid)&&(g=await L(a,!0)));f&&(y._f.deps&&en(y._f.deps),_(d,g,l,V))}},ea=(e,t)=>{if(T(s.errors,t)&&e.focus)return e.focus(),1},en=async(e,t={})=>{let i,n,l=z(e);if(r.resolver){let t=await M(D(e)?e:l);i=J(t),n=e?!l.some(e=>T(t,e)):i}else e?((n=(await Promise.all(l.map(async e=>{let t=T(a,e);return await L(t&&t._f?{[e]:t}:t)}))).every(Boolean))||s.isValid)&&x():n=i=await L(a);return m.state.next({...!R(e)||(c.isValid||u.isValid)&&i!==s.isValid?{}:{name:e},...r.resolver||!e?{isValid:i}:{},errors:s.errors}),t.shouldFocus&&!n&&ej(a,ea,e?l:o.mount),n},el=e=>{let t={...l.mount?n:i};return D(e)?t:R(e)?T(t,e):e.map(e=>T(t,e))},eo=(e,t)=>({invalid:!!T((t||s).errors,e),isDirty:!!T((t||s).dirtyFields,e),error:T((t||s).errors,e),isValidating:!!T(s.validatingFields,e),isTouched:!!T((t||s).touchedFields,e)}),ec=(e,t,r)=>{let i=(T(a,e,{_f:{}})._f||{}).ref,{ref:n,message:l,type:o,...d}=T(s.errors,e)||{};P(s.errors,e,{...d,...t,ref:i}),m.state.next({name:e,errors:s.errors,isValid:!1}),r&&r.shouldFocus&&i&&i.focus&&i.focus()},eu=e=>m.state.subscribe({next:t=>{eN(e.name,t.name,e.exact)&&ek(t,e.formState||c,eE,e.reRenderRoot)&&e.callback({values:{...n},...s,...t})}}).unsubscribe,ef=(e,t={})=>{for(let l of e?z(e):o.mount)o.mount.delete(l),o.array.delete(l),t.keepValue||(er(a,l),er(n,l)),t.keepError||er(s.errors,l),t.keepDirty||er(s.dirtyFields,l),t.keepTouched||er(s.touchedFields,l),t.keepIsValidating||er(s.validatingFields,l),r.shouldUnregister||t.keepDefaultValue||er(i,l);m.state.next({values:C(n)}),m.state.next({...s,...!t.keepDirty?{}:{isDirty:$()}}),t.keepIsValid||x()},ey=({disabled:e,name:t})=>{(O(e)&&l.mount||e||o.disabled.has(t))&&(e?o.disabled.add(t):o.disabled.delete(t))},e_=(e,t={})=>{let s=T(a,e),n=O(t.disabled)||O(r.disabled);return P(a,e,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:e}},name:e,mount:!0,...t}}),o.mount.add(e),s?ey({disabled:O(t.disabled)?t.disabled:r.disabled,name:e}):k(e,!0,t.value),{...n?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:ex(t.min),max:ex(t.max),minLength:ex(t.minLength),maxLength:ex(t.maxLength),pattern:ex(t.pattern)}:{},name:e,onChange:es,onBlur:es,ref:n=>{if(n){e_(e,t),s=T(a,e);let r=D(n.value)&&n.querySelectorAll&&n.querySelectorAll("input,select,textarea")[0]||n,l=ee(r),o=s._f.refs||[];(l?o.find(e=>e===r):r===s._f.ref)||(P(a,e,{_f:{...s._f,...l?{refs:[...o.filter(et),r,...Array.isArray(T(i,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),k(e,!1,void 0,r))}else(s=T(a,e,{}))._f&&(s._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(N(o.array,e)&&l.action)&&o.unMount.add(e)}}},eD=()=>r.shouldFocusError&&ej(a,ea,o.mount),eV=(e,t)=>async i=>{let l;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let d=C(n);if(m.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await F();s.errors=e,d=C(t)}else await L(a);if(o.disabled.size)for(let e of o.disabled)er(d,e);if(er(s.errors,"root"),J(s.errors)){m.state.next({errors:{}});try{await e(d,i)}catch(e){l=e}}else t&&await t({...s.errors},i),eD(),setTimeout(eD);if(m.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:J(s.errors)&&!l,submitCount:s.submitCount+1,errors:s.errors}),l)throw l},eO=(e,t={})=>{let d=e?C(e):i,u=C(d),h=J(e),f=h?i:u;if(t.keepDefaultValues||(i=d),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...o.mount,...Object.keys(ei(i,n))])))T(s.dirtyFields,e)?P(f,e,T(n,e)):Q(e,T(f,e));else{if(S&&D(e))for(let e of o.mount){let t=T(a,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(X(e)){let t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(let e of o.mount)Q(e,T(f,e));else a={}}n=r.shouldUnregister?t.keepDefaultValues?C(i):{}:C(f),m.array.next({values:{...f}}),m.state.next({values:{...f}})}o={mount:t.keepDirtyValues?o.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},l.mount=!c.isValid||!!t.keepIsValid||!!t.keepDirtyValues,l.watch=!!r.shouldUnregister,m.state.next({submitCount:t.keepSubmitCount?s.submitCount:0,isDirty:!h&&(t.keepDirty?s.isDirty:!!(t.keepDefaultValues&&!G(e,i))),isSubmitted:!!t.keepIsSubmitted&&s.isSubmitted,dirtyFields:h?{}:t.keepDirtyValues?t.keepDefaultValues&&n?ei(i,n):s.dirtyFields:t.keepDefaultValues&&e?ei(i,e):t.keepDirty?s.dirtyFields:{},touchedFields:t.keepTouched?s.touchedFields:{},errors:t.keepErrors?s.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&s.isSubmitSuccessful,isSubmitting:!1})},eP=(e,t)=>eO(Z(e)?e(n):e,t),eE=e=>{s={...s,...e}},eU={control:{register:e_,unregister:ef,getFieldState:eo,handleSubmit:eV,setError:ec,_subscribe:eu,_runSchema:F,_focusError:eD,_getWatch:q,_getDirty:$,_setValid:x,_setFieldArray:(e,t=[],o,d,h=!0,f=!0)=>{if(d&&o&&!r.disabled){if(l.action=!0,f&&Array.isArray(T(a,e))){let t=o(T(a,e),d.argA,d.argB);h&&P(a,e,t)}if(f&&Array.isArray(T(s.errors,e))){let t=o(T(s.errors,e),d.argA,d.argB);h&&P(s.errors,e,t),eS(s.errors,e)}if((c.touchedFields||u.touchedFields)&&f&&Array.isArray(T(s.touchedFields,e))){let t=o(T(s.touchedFields,e),d.argA,d.argB);h&&P(s.touchedFields,e,t)}(c.dirtyFields||u.dirtyFields)&&(s.dirtyFields=ei(i,n)),m.state.next({name:e,isDirty:$(e,t),dirtyFields:s.dirtyFields,errors:s.errors,isValid:s.isValid})}else P(n,e,t)},_setDisabledField:ey,_setErrors:e=>{s.errors=e,m.state.next({errors:s.errors,isValid:!1})},_getFieldArray:e=>V(T(l.mount?n:i,e,r.shouldUnregister?T(i,e,[]):[])),_reset:eO,_resetDefaultValues:()=>Z(r.defaultValues)&&r.defaultValues().then(e=>{eP(e,r.resetOptions),m.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of o.unMount){let t=T(a,e);t&&(t._f.refs?t._f.refs.every(e=>!et(e)):!et(t._f.ref))&&ef(e)}o.unMount=new Set},_disableForm:e=>{O(e)&&(m.state.next({disabled:e}),ej(a,(t,r)=>{let s=T(a,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach(t=>{t.disabled=s._f.disabled||e}))},0,!1))},_subjects:m,_proxyFormState:c,get _fields(){return a},get _formValues(){return n},get _state(){return l},set _state(value){l=value},get _defaultValues(){return i},get _names(){return o},set _names(value){o=value},get _formState(){return s},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(l.mount=!0,u={...u,...e.formState},eu({...e,formState:u})),trigger:en,register:e_,handleSubmit:eV,watch:(e,t)=>Z(e)?m.state.subscribe({next:r=>e(q(void 0,t),r)}):q(e,t,!0),setValue:Q,getValues:el,reset:eP,resetField:(e,t={})=>{T(a,e)&&(D(t.defaultValue)?Q(e,C(T(i,e))):(Q(e,t.defaultValue),P(i,e,C(t.defaultValue))),t.keepTouched||er(s.touchedFields,e),t.keepDirty||(er(s.dirtyFields,e),s.isDirty=t.defaultValue?$(e,C(T(i,e))):$()),!t.keepError&&(er(s.errors,e),c.isValid&&x()),m.state.next({...s}))},clearErrors:e=>{e&&z(e).forEach(e=>er(s.errors,e)),m.state.next({errors:e?s.errors:{}})},unregister:ef,setError:ec,setFocus:(e,t={})=>{let r=T(a,e),s=r&&r._f;if(s){let e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&Z(e.select)&&e.select())}},getFieldState:eo};return{...eU,formControl:eU}}(e);t.current={...a,formState:s}}let i=t.current.control;return i._options=e,q(()=>{let e=i._subscribe({formState:i._proxyFormState,callback:()=>a({...i._formState}),reRenderRoot:!0});return a(e=>({...e,isReady:!0})),i._formState.isReady=!0,e},[i]),p.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),p.useEffect(()=>{e.mode&&(i._options.mode=e.mode),e.reValidateMode&&(i._options.reValidateMode=e.reValidateMode)},[i,e.mode,e.reValidateMode]),p.useEffect(()=>{e.errors&&(i._setErrors(e.errors),i._focusError())},[i,e.errors]),p.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[i,e.shouldUnregister]),p.useEffect(()=>{if(i._proxyFormState.isDirty){let e=i._getDirty();e!==s.isDirty&&i._subjects.state.next({isDirty:e})}},[i,s.isDirty]),p.useEffect(()=>{e.values&&!G(e.values,r.current)?(i._reset(e.values,{keepFieldsRef:!0,...i._options.resetOptions}),r.current=e.values,a(e=>({...e}))):i._resetDefaultValues()},[i,e.values]),p.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()}),t.current.formState=$(s,i),t.current}({defaultValues:{title:e?.title||"",description:e?.description||"",priority:e?.priority||"medium",status:e?.status||"todo",dueDate:e?.dueDate?new Date(e.dueDate).toISOString().split("T")[0]:""}}),k=async e=>{l(!0);try{let r={title:e.title.trim(),description:e.description.trim(),priority:e.priority,status:e.status,dueDate:e.dueDate?new Date(e.dueDate).toISOString():null};await t(r)}catch(e){console.error("Form submission error:",e)}finally{l(!1)}},A=()=>{m(),r()};return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-secondary-200",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-secondary-900",children:o?"Edit Task":"Create New Task"}),(0,s.jsx)("button",{onClick:A,className:"p-2 text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200",children:(0,s.jsx)(n,{className:"w-5 h-5"})})]}),(0,s.jsxs)("form",{onSubmit:c(k),className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"title",className:"form-label",children:"Task Title *"}),(0,s.jsx)("input",{id:"title",type:"text",...d("title",{required:"Task title is required",minLength:{value:1,message:"Title must be at least 1 character"},maxLength:{value:200,message:"Title must be less than 200 characters"}}),className:`form-input ${u.title?"border-danger-300 focus:border-danger-500 focus:ring-danger-500":""}`,placeholder:"Enter task title...",disabled:i||a}),u.title&&(0,s.jsx)("p",{className:"form-error",children:u.title.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"description",className:"form-label",children:"Description"}),(0,s.jsx)("textarea",{id:"description",rows:3,...d("description",{maxLength:{value:1e3,message:"Description must be less than 1000 characters"}}),className:`form-input ${u.description?"border-danger-300 focus:border-danger-500 focus:ring-danger-500":""}`,placeholder:"Enter task description...",disabled:i||a}),u.description&&(0,s.jsx)("p",{className:"form-error",children:u.description.message})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"priority",className:"form-label flex items-center space-x-1",children:[(0,s.jsx)(h,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Priority"})]}),(0,s.jsxs)("select",{id:"priority",...d("priority"),className:"form-input",disabled:i||a,children:[(0,s.jsx)("option",{value:"low",children:"Low"}),(0,s.jsx)("option",{value:"medium",children:"Medium"}),(0,s.jsx)("option",{value:"high",children:"High"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"status",className:"form-label flex items-center space-x-1",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Status"})]}),(0,s.jsxs)("select",{id:"status",...d("status"),className:"form-input",disabled:i||a,children:[(0,s.jsx)("option",{value:"todo",children:"To Do"}),(0,s.jsx)("option",{value:"in-progress",children:"In Progress"}),(0,s.jsx)("option",{value:"completed",children:"Completed"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"dueDate",className:"form-label flex items-center space-x-1",children:[(0,s.jsx)(x,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Due Date"})]}),(0,s.jsx)("input",{id:"dueDate",type:"date",...d("dueDate"),className:"form-input",disabled:i||a,min:new Date().toISOString().split("T")[0]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end space-x-3 pt-4 border-t border-secondary-200",children:[(0,s.jsx)("button",{type:"button",onClick:A,className:"btn-outline",disabled:i||a,children:"Cancel"}),(0,s.jsxs)("button",{type:"submit",className:"btn-primary flex items-center space-x-2",disabled:i||a,children:[(i||a)&&(0,s.jsx)("div",{className:"spinner w-4 h-4"}),(0,s.jsx)("span",{children:i||a?o?"Updating...":"Creating...":o?"Update Task":"Create Task"})]})]})]})]})})}var eP=r(3689);let eE=(0,i.A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),eU=(0,i.A)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]),eM=(0,i.A)("Pen",[["path",{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z",key:"5qss01"}]]),eL=(0,i.A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),e$=(0,i.A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function eq({isOpen:e,title:t,message:r,confirmText:a="Confirm",cancelText:i="Cancel",variant:l="danger",onConfirm:o,onCancel:d,isLoading:c=!1}){if(!e)return null;let u={danger:{icon:"text-danger-600",button:"btn-danger"},warning:{icon:"text-warning-600",button:"btn-warning"},info:{icon:"text-primary-600",button:"btn-primary"}}[l];return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-xl max-w-md w-full",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-secondary-200",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:`w-8 h-8 rounded-full bg-opacity-20 flex items-center justify-center ${"danger"===l?"bg-danger-100":"warning"===l?"bg-warning-100":"bg-primary-100"}`,children:(0,s.jsx)(e$,{className:`w-5 h-5 ${u.icon}`})}),(0,s.jsx)("h2",{className:"text-lg font-semibold text-secondary-900",children:t})]}),!c&&(0,s.jsx)("button",{onClick:d,className:"p-2 text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200",children:(0,s.jsx)(n,{className:"w-5 h-5"})})]}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("p",{className:"text-secondary-600 leading-relaxed",children:r})}),(0,s.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-secondary-200 bg-secondary-50 rounded-b-xl",children:[(0,s.jsx)("button",{onClick:d,disabled:c,className:"btn-outline",children:i}),(0,s.jsxs)("button",{onClick:o,disabled:c,className:`${u.button} flex items-center space-x-2`,children:[c&&(0,s.jsx)("div",{className:"spinner w-4 h-4"}),(0,s.jsx)("span",{children:c?"Processing...":a})]})]})]})})}function eR({tasks:e,onEdit:t,onDelete:r,onUpdate:i}){let[n,l]=(0,p.useState)(null),[o,d]=(0,p.useState)(null),c=e=>{l(e),d(null)},u=e=>{t(e),d(null)},m=e=>{switch(e){case"high":return"text-danger-600 bg-danger-50";case"medium":return"text-warning-600 bg-warning-50";case"low":return"text-success-600 bg-success-50";default:return"text-secondary-600 bg-secondary-50"}},y=e=>{switch(e){case"completed":return(0,s.jsx)(eP.A,{className:"w-5 h-5 text-success-600"});case"in-progress":return(0,s.jsx)(f.A,{className:"w-5 h-5 text-warning-600"});default:return(0,s.jsx)(eE,{className:"w-5 h-5 text-secondary-400"})}},g=e=>{switch(e){case"completed":return"text-success-700 bg-success-50 border-success-200";case"in-progress":return"text-warning-700 bg-warning-50 border-warning-200";default:return"text-secondary-700 bg-secondary-50 border-secondary-200"}},b=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),v=e=>!!e&&new Date(e)<new Date;return 0===e.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-secondary-100 rounded-full flex items-center justify-center",children:(0,s.jsx)(eP.A,{className:"w-8 h-8 text-secondary-400"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-secondary-900 mb-2",children:"No tasks found"}),(0,s.jsx)("p",{className:"text-secondary-600",children:"Create your first task to get started with managing your work."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"divide-y divide-secondary-200",children:e.map(e=>(0,s.jsx)("div",{className:"p-4 hover:bg-secondary-50 transition-colors duration-200",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-3 flex-1",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-1",children:y(e.status)}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"flex items-start justify-between",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:`text-sm font-medium ${"completed"===e.status?"text-secondary-500 line-through":"text-secondary-900"}`,children:e.title}),e.description&&(0,s.jsx)("p",{className:`mt-1 text-sm ${"completed"===e.status?"text-secondary-400":"text-secondary-600"}`,children:e.description})]})}),(0,s.jsxs)("div",{className:"mt-3 flex items-center space-x-4 text-xs",children:[(0,s.jsxs)("span",{className:`inline-flex items-center px-2 py-1 rounded-full font-medium ${m(e.priority)}`,children:[(0,s.jsx)(h,{className:"w-3 h-3 mr-1"}),e.priority]}),(0,s.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full border font-medium ${g(e.status)}`,children:e.status.replace("-"," ")}),e.dueDate&&(0,s.jsxs)("span",{className:`inline-flex items-center ${v(e.dueDate)?"text-danger-600":"text-secondary-500"}`,children:[(0,s.jsx)(x,{className:"w-3 h-3 mr-1"}),b(e.dueDate),v(e.dueDate)&&(0,s.jsx)(a.A,{className:"w-3 h-3 ml-1"})]}),(0,s.jsxs)("span",{className:"text-secondary-400",children:["Created ",b(e.createdAt)]})]})]})]}),(0,s.jsxs)("div",{className:"relative flex-shrink-0 ml-4",children:[(0,s.jsx)("button",{onClick:()=>d(o===e.id?null:e.id),className:"p-1 rounded-md text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 transition-colors duration-200",children:(0,s.jsx)(eU,{className:"w-4 h-4"})}),o===e.id&&(0,s.jsx)("div",{className:"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-secondary-200 z-10",children:(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsxs)("button",{onClick:()=>u(e),className:"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-200",children:[(0,s.jsx)(eM,{className:"w-4 h-4 mr-3"}),"Edit Task"]}),(0,s.jsxs)("button",{onClick:()=>c(e.id),className:"flex items-center w-full px-4 py-2 text-sm text-danger-700 hover:bg-danger-50 transition-colors duration-200",children:[(0,s.jsx)(eL,{className:"w-4 h-4 mr-3"}),"Delete Task"]})]})})]})]})},e.id))}),(0,s.jsx)(eq,{isOpen:!!n,title:"Delete Task",message:"Are you sure you want to delete this task? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",onConfirm:()=>{n&&(r(n),l(null))},onCancel:()=>{l(null)},variant:"danger"}),o&&(0,s.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>d(null)})]})}let eI=(0,i.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);function eB({id:e,type:t,title:r,message:i,duration:l=5e3,onClose:o}){let[d,c]=(0,p.useState)(!1),[u,m]=(0,p.useState)(!1);return(0,s.jsx)("div",{className:`
        transform transition-all duration-300 ease-in-out
        ${d&&!u?"translate-x-0 opacity-100":"translate-x-full opacity-0"}
        ${(()=>{switch(t){case"success":return"bg-success-50 border-success-200";case"error":return"bg-danger-50 border-danger-200";case"warning":return"bg-warning-50 border-warning-200";default:return"bg-primary-50 border-primary-200"}})()}
        border rounded-lg shadow-lg p-4 mb-3 max-w-sm w-full
      `,children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(t){case"success":return(0,s.jsx)(eP.A,{className:"w-5 h-5 text-success-600"});case"error":return(0,s.jsx)(a.A,{className:"w-5 h-5 text-danger-600"});case"warning":return(0,s.jsx)(a.A,{className:"w-5 h-5 text-warning-600"});default:return(0,s.jsx)(eI,{className:"w-5 h-5 text-primary-600"})}})()}),(0,s.jsxs)("div",{className:"ml-3 flex-1",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-secondary-900",children:r}),i&&(0,s.jsx)("p",{className:"mt-1 text-sm text-secondary-600",children:i})]}),(0,s.jsx)("button",{onClick:()=>{m(!0),setTimeout(()=>{o(e)},300)},className:"ml-4 flex-shrink-0 p-1 rounded-md text-secondary-400 hover:text-secondary-600 transition-colors duration-200",children:(0,s.jsx)(n,{className:"w-4 h-4"})})]})})}function ez({toasts:e,onClose:t}){return(0,s.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:e.map(e=>(0,s.jsx)(eB,{...e,onClose:t},e.id))})}let eW=(0,i.A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),eH=(0,i.A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),eG=(0,i.A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]),eJ=(0,i.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),eY=(0,i.A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);function eZ({user:e,onLogout:t}){let[r,a]=(0,p.useState)(!1),i=(0,p.useRef)(null),n=e=>e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2);return(0,s.jsxs)("div",{className:"relative",ref:i,children:[(0,s.jsxs)("button",{onClick:()=>a(!r),className:"flex items-center space-x-3 p-2 rounded-lg hover:bg-secondary-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white text-sm font-medium",children:n(e.name)}),(0,s.jsxs)("div",{className:"hidden md:block text-left",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-secondary-900",children:e.name}),(0,s.jsx)("p",{className:"text-xs text-secondary-600",children:e.email})]}),(0,s.jsx)(eW,{className:`w-4 h-4 text-secondary-600 transition-transform duration-200 ${r?"transform rotate-180":""}`})]}),r&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-secondary-200 py-2 z-50",children:[(0,s.jsxs)("div",{className:"px-4 py-3 border-b border-secondary-200",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center text-white font-medium",children:n(e.name)}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-secondary-900 truncate",children:e.name}),(0,s.jsx)("p",{className:"text-xs text-secondary-600 truncate",children:e.email}),e.username&&(0,s.jsxs)("p",{className:"text-xs text-secondary-500 truncate",children:["@",e.username]})]})]}),e.roles&&e.roles.length>0||e.groups&&e.groups.length>0?(0,s.jsxs)("div",{className:"mt-2 flex flex-wrap gap-1",children:[e.roles?.map(e=>(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800",children:e},e)),e.groups?.map(e=>(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-secondary-100 text-secondary-800",children:e},e))]}):null]}),(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsxs)("button",{onClick:()=>{a(!1),console.log("View profile clicked")},className:"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 transition-colors duration-200",children:[(0,s.jsx)(eH,{className:"w-4 h-4 mr-3"}),"View Profile"]}),(0,s.jsxs)("button",{onClick:()=>{a(!1),console.log("View activity clicked")},className:"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 transition-colors duration-200",children:[(0,s.jsx)(eG,{className:"w-4 h-4 mr-3"}),"Activity"]}),(0,s.jsxs)("button",{onClick:()=>{a(!1),console.log("Settings clicked")},className:"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 transition-colors duration-200",children:[(0,s.jsx)(eJ,{className:"w-4 h-4 mr-3"}),"Settings"]})]}),(0,s.jsx)("div",{className:"border-t border-secondary-200 py-1",children:(0,s.jsxs)("button",{onClick:()=>{a(!1),t()},className:"flex items-center w-full px-4 py-2 text-sm text-danger-700 hover:bg-danger-50 transition-colors duration-200",children:[(0,s.jsx)(eY,{className:"w-4 h-4 mr-3"}),"Sign Out"]})})]})]})}var eX=r(4604);class eK extends Error{constructor(e,t,r){super(e),this.status=t,this.response=r,this.name="ApiClientError"}}class eQ{constructor(e="/choreo-apis"){this.baseUrl=e}async request(e,t={}){let r=`${this.baseUrl}${e}`,s={credentials:"include",headers:{"Content-Type":"application/json",Accept:"application/json",...t.headers},...t};try{let e=await fetch(r,s);if(401===e.status)throw(0,eX.en)(),new eK("Authentication required",401);let t=await e.json();if(!e.ok)throw new eK(t.message||`HTTP ${e.status}`,e.status,t);return t}catch(e){if(e instanceof eK)throw e;throw console.error("API request failed:",e),new eK("Network error or server unavailable",0,e)}}async getTasks(e){let t=new URLSearchParams;e?.status&&t.append("status",e.status),e?.priority&&t.append("priority",e.priority),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder);let r=t.toString(),s=`/tasks${r?`?${r}`:""}`;return this.request(s)}async getTask(e){return this.request(`/tasks/${e}`)}async createTask(e){return this.request("/tasks",{method:"POST",body:JSON.stringify(e)})}async updateTask(e,t){return this.request(`/tasks/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTask(e){return this.request(`/tasks/${e}`,{method:"DELETE"})}async getTaskStats(){return this.request("/tasks/stats")}async getUserProfile(){return this.request("/user/profile")}async getUserPreferences(){return this.request("/user/preferences")}async updateUserPreferences(e){return this.request("/user/preferences",{method:"PUT",body:JSON.stringify(e)})}async getUserActivity(){return this.request("/user/activity")}async logoutUser(){return this.request("/user/logout",{method:"POST"})}async getUserPermissions(){return this.request("/user/permissions")}}let e0=new eQ,e1={getAll:e=>e0.getTasks(e),create:e=>e0.createTask(e),update:(e,t)=>e0.updateTask(e,t),delete:e=>e0.deleteTask(e),getStats:()=>e0.getTaskStats()},e2=e=>e instanceof eK,e4=e=>e2(e)||e instanceof Error?e.message:"An unexpected error occurred",e3=(0,i.A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),e5=(0,i.A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),e6=(0,i.A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);function e8(){let[e,t]=(0,p.useState)(null),{toasts:r,removeToast:i,success:n,error:o}=function(){let[e,t]=(0,p.useState)([]),r=e=>{let r=Math.random().toString(36).substr(2,9),a={...e,id:r,onClose:s};t(e=>[...e,a])},s=e=>{t(t=>t.filter(t=>t.id!==e))};return{toasts:e,addToast:r,removeToast:s,success:(e,t)=>{r({type:"success",title:e,message:t})},error:(e,t)=>{r({type:"error",title:e,message:t})},warning:(e,t)=>{r({type:"warning",title:e,message:t})},info:(e,t)=>{r({type:"info",title:e,message:t})}}}(),[d,c]=(0,p.useState)([]),[h,x]=(0,p.useState)(null),[y,g]=(0,p.useState)({}),[b,v]=(0,p.useState)(!0),[j,w]=(0,p.useState)(null),[k,N]=(0,p.useState)(!1),[A,S]=(0,p.useState)(null),[C,_]=(0,p.useState)(""),D=async()=>{try{let e=await e1.getAll(y);c(e.tasks)}catch(e){console.error("Error loading tasks:",e),w(e4(e))}},V=async()=>{try{let e=await e1.getStats();x(e.stats)}catch(e){console.error("Error loading task stats:",e)}},F=async e=>{try{let t=await e1.create(e);c(e=>[t.task,...e]),N(!1),await V(),n("Task created","Your new task has been created successfully")}catch(e){console.error("Error creating task:",e),o("Failed to create task",e4(e))}},T=async e=>{if(A)try{let t=await e1.update(A.id,e);c(e=>e.map(e=>e.id===A.id?t.task:e)),S(null),await V(),n("Task updated","Your task has been updated successfully")}catch(e){console.error("Error updating task:",e),o("Failed to update task",e4(e))}},O=async e=>{try{await e1.delete(e),c(t=>t.filter(t=>t.id!==e)),await V()}catch(e){console.error("Error deleting task:",e),w(e4(e))}},P=d.filter(e=>e.title.toLowerCase().includes(C.toLowerCase())||e.description.toLowerCase().includes(C.toLowerCase()));return b?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-secondary-50",children:(0,s.jsx)(m.A,{size:"large"})}):(0,s.jsxs)("div",{className:"min-h-screen bg-secondary-50",children:[(0,s.jsx)(ez,{toasts:r,onClose:i}),(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-secondary-200",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)(eP.A,{className:"w-5 h-5 text-white"})}),(0,s.jsx)("h1",{className:"text-xl font-bold text-secondary-900",children:"Task Management"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("button",{onClick:()=>N(!0),className:"btn-primary flex items-center space-x-2",children:[(0,s.jsx)(e3,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"New Task"})]}),e&&(0,s.jsx)(eZ,{user:e,onLogout:()=>{eX.y1.logout()}})]})]})})}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[j&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(l,{message:j,onDismiss:()=>w(null)})}),h&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,s.jsx)("div",{className:"card p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-secondary-600",children:"Total Tasks"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-secondary-900",children:h.total})]}),(0,s.jsx)(e5,{className:"w-8 h-8 text-primary-600"})]})}),(0,s.jsx)("div",{className:"card p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-secondary-600",children:"In Progress"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-warning-600",children:h.byStatus["in-progress"]})]}),(0,s.jsx)(f.A,{className:"w-8 h-8 text-warning-600"})]})}),(0,s.jsx)("div",{className:"card p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-secondary-600",children:"Completed"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-success-600",children:h.byStatus.completed})]}),(0,s.jsx)(eP.A,{className:"w-8 h-8 text-success-600"})]})}),(0,s.jsx)("div",{className:"card p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-secondary-600",children:"Overdue"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-danger-600",children:h.overdue})]}),(0,s.jsx)(a.A,{className:"w-8 h-8 text-danger-600"})]})})]}),(0,s.jsx)("div",{className:"card mb-6",children:(0,s.jsx)("div",{className:"card-body",children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,s.jsx)("div",{className:"flex-1 max-w-md",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(e6,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search tasks...",value:C,onChange:e=>_(e.target.value),className:"form-input pl-10"})]})}),h&&(0,s.jsx)(u,{filters:y,onFiltersChange:e=>{g(e)},taskStats:h})]})})}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsx)("div",{className:"card-header",children:(0,s.jsxs)("h2",{className:"text-lg font-semibold text-secondary-900",children:["Your Tasks (",P.length,")"]})}),(0,s.jsx)("div",{className:"card-body p-0",children:(0,s.jsx)(eR,{tasks:P,onEdit:e=>{S(e),N(!0)},onDelete:O,onUpdate:D})})]})]}),k&&(0,s.jsx)(eO,{task:A,onSubmit:A?T:F,onCancel:()=>{N(!1),S(null)}})]})}},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6450:(e,t,r)=>{Promise.resolve().then(r.bind(r,3902))},6569:()=>{},6698:(e,t,r)=>{Promise.resolve().then(r.bind(r,3144))},7763:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},8249:(e,t,r)=>{Promise.resolve().then(r.bind(r,6020))},8730:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(8962).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},8962:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(3210),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),n=(e,t)=>{let r=(0,s.forwardRef)(({color:r="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:d="",children:c,...u},m)=>(0,s.createElement)("svg",{ref:m,...a,width:n,height:n,stroke:r,strokeWidth:o?24*Number(l)/Number(n):l,className:["lucide",`lucide-${i(e)}`,d].join(" "),...u},[...t.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[945],()=>r(4625));module.exports=s})();