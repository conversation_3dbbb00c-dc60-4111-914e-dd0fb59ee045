(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{388:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(5155),n=r(2115),i=r(5695),a=r(9323),o=r(2731);function c(e){let{children:t}=e,[r,c]=(0,n.useState)(!0),[u,l]=(0,n.useState)(!1);(0,i.useRouter)(),(0,n.useEffect)(()=>{h()},[]);let h=async()=>{try{if(a.y1.getUserInfoFromCookie()){l(!0),c(!1);return}if(!(await a.y1.checkAuthStatus()).isAuthenticated)return void a.y1.login("/dashboard");l(!0)}catch(e){console.error("Authentication check failed:",e),a.y1.login("/dashboard");return}finally{c(!1)}};return r?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-secondary-50",children:(0,s.jsx)(o.A,{size:"large",text:"Checking authentication..."})}):u?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-secondary-50",children:(0,s.jsx)(o.A,{size:"large",text:"Redirecting to login..."})})}},2731:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(5155);function n(e){let{size:t="medium",className:r="",text:n}=e;return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(r),children:[(0,s.jsx)("div",{className:"spinner ".concat({small:"w-4 h-4",medium:"w-6 h-6",large:"w-8 h-8"}[t])}),n&&(0,s.jsx)("p",{className:"mt-2 text-sm text-secondary-600",children:n})]})}},3777:(e,t,r)=>{Promise.resolve().then(r.bind(r,388))},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},7383:(e,t,r)=>{"use strict";function s(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)e[s]=r[s]}return e}r.d(t,{A:()=>n});var n=function e(t,r){function n(e,n,i){if("undefined"!=typeof document){"number"==typeof(i=s({},r,i)).expires&&(i.expires=new Date(Date.now()+864e5*i.expires)),i.expires&&(i.expires=i.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var o in i)i[o]&&(a+="; "+o,!0!==i[o]&&(a+="="+i[o].split(";")[0]));return document.cookie=e+"="+t.write(n,e)+a}}return Object.create({set:n,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var r=document.cookie?document.cookie.split("; "):[],s={},n=0;n<r.length;n++){var i=r[n].split("="),a=i.slice(1).join("=");try{var o=decodeURIComponent(i[0]);if(s[o]=t.read(a,o),e===o)break}catch(e){}}return e?s[e]:s}},remove:function(e,t){n(e,"",s({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,s({},this.attributes,t))},withConverter:function(t){return e(s({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})},9323:(e,t,r)=>{"use strict";r.d(t,{en:()=>a,y1:()=>i});var s=r(7383);class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}login(e){let t="/auth/login",r=e?"".concat(t,"?redirect=").concat(encodeURIComponent(e)):t;window.location.href=r}logout(){let e=s.A.get("session_hint");this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),window.location.href=e?"/auth/logout?session_hint=".concat(e):"/auth/logout"}getUserInfoFromCookie(){try{let e=s.A.get("userinfo");if(!e)return null;let t=JSON.parse(atob(e));return s.A.remove("userinfo",{path:"/"}),this.user={id:t.sub,email:t.email,name:t.name||"".concat(t.given_name||""," ").concat(t.family_name||"").trim(),username:t.preferred_username,groups:t.groups||[],roles:t.roles||[],profileComplete:!!(t.name&&t.email)},this.isAuthenticated=!0,this.storeUserData(this.user),this.user}catch(e){return console.error("Error parsing user info cookie:",e),null}}async checkAuthStatus(){try{let e=await fetch("/auth/userinfo",{method:"GET",credentials:"include",headers:{Accept:"application/json"}});if(e.ok){let t=await e.json();return this.user={id:t.sub,email:t.email,name:t.name||"".concat(t.given_name||""," ").concat(t.family_name||"").trim(),username:t.preferred_username,groups:t.groups||[],roles:t.roles||[],profileComplete:!!(t.name&&t.email)},this.isAuthenticated=!0,this.storeUserData(this.user),{isAuthenticated:!0,user:this.user}}if(401===e.status)return this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),{isAuthenticated:!1,user:null};throw Error("Auth check failed with status: ".concat(e.status))}catch(t){console.error("Error checking auth status:",t);let e=this.getStoredUserData();if(e)return this.user=e,this.isAuthenticated=!0,{isAuthenticated:!0,user:e};return{isAuthenticated:!1,user:null}}}getCurrentUser(){if(this.user)return this.user;let e=this.getStoredUserData();return e?(this.user=e,this.isAuthenticated=!0,e):null}getIsAuthenticated(){return this.isAuthenticated||!!this.getStoredUserData()}handleSessionExpiry(){console.log("Session expired, redirecting to login..."),this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),this.login(window.location.pathname)}storeUserData(e){try{localStorage.setItem("user",JSON.stringify(e)),localStorage.setItem("lastLogin",new Date().toISOString())}catch(e){console.error("Error storing user data:",e)}}getStoredUserData(){try{let e=localStorage.getItem("user");if(e)return JSON.parse(e)}catch(e){console.error("Error retrieving stored user data:",e)}return null}clearStoredUserData(){try{localStorage.removeItem("user"),localStorage.removeItem("lastLogin")}catch(e){console.error("Error clearing stored user data:",e)}}constructor(){this.user=null,this.isAuthenticated=!1}}let i=n.getInstance(),a=()=>i.handleSessionExpiry()}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(3777)),_N_E=e.O()}]);