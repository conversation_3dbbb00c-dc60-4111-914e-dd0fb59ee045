(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(5155);function a(e){let{size:s="medium",className:t="",text:a}=e;return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(t),children:[(0,r.jsx)("div",{className:"spinner ".concat({small:"w-4 h-4",medium:"w-6 h-6",large:"w-8 h-8"}[s])}),a&&(0,r.jsx)("p",{className:"mt-2 text-sm text-secondary-600",children:a})]})}},4337:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>R});var r=t(5155),a=t(8533),n=t(4416);function i(e){let{message:s,onDismiss:t,className:i="",variant:l="error"}=e,c={error:"text-danger-600",warning:"text-warning-600",info:"text-primary-600"};return(0,r.jsx)("div",{className:"rounded-lg border p-4 ".concat({error:"bg-danger-50 border-danger-200 text-danger-800",warning:"bg-warning-50 border-warning-200 text-warning-800",info:"bg-primary-50 border-primary-200 text-primary-800"}[l]," ").concat(i),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 mt-0.5 mr-3 flex-shrink-0 ".concat(c[l])}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-sm font-medium",children:s})}),t&&(0,r.jsx)("button",{onClick:t,className:"ml-3 flex-shrink-0 p-1 rounded-md hover:bg-opacity-20 hover:bg-current transition-colors duration-200 ".concat(c[l]),children:(0,r.jsx)(n.A,{className:"w-4 h-4"})})]})})}var l=t(5488),c=t(5213),o=t(1482);function d(e){let{filters:s,onFiltersChange:t,taskStats:a}=e,i=(e,r)=>{let a={...s};""===r||void 0===r?delete a[e]:a[e]=r,t(a)},d=Object.keys(s).length>0;return(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{htmlFor:"status-filter",className:"text-sm font-medium text-secondary-700",children:"Status:"}),(0,r.jsxs)("select",{id:"status-filter",value:s.status||"",onChange:e=>i("status",e.target.value),className:"text-sm border-secondary-300 rounded-md focus:border-primary-500 focus:ring-primary-500",children:[(0,r.jsxs)("option",{value:"",children:["All (",a.total,")"]}),(0,r.jsxs)("option",{value:"todo",children:["To Do (",a.byStatus.todo,")"]}),(0,r.jsxs)("option",{value:"in-progress",children:["In Progress (",a.byStatus["in-progress"],")"]}),(0,r.jsxs)("option",{value:"completed",children:["Completed (",a.byStatus.completed,")"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{htmlFor:"priority-filter",className:"text-sm font-medium text-secondary-700",children:"Priority:"}),(0,r.jsxs)("select",{id:"priority-filter",value:s.priority||"",onChange:e=>i("priority",e.target.value),className:"text-sm border-secondary-300 rounded-md focus:border-primary-500 focus:ring-primary-500",children:[(0,r.jsx)("option",{value:"",children:"All"}),(0,r.jsxs)("option",{value:"high",children:["High (",a.byPriority.high,")"]}),(0,r.jsxs)("option",{value:"medium",children:["Medium (",a.byPriority.medium,")"]}),(0,r.jsxs)("option",{value:"low",children:["Low (",a.byPriority.low,")"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{htmlFor:"sort-filter",className:"text-sm font-medium text-secondary-700",children:"Sort by:"}),(0,r.jsxs)("select",{id:"sort-filter",value:s.sortBy||"createdAt",onChange:e=>i("sortBy",e.target.value),className:"text-sm border-secondary-300 rounded-md focus:border-primary-500 focus:ring-primary-500",children:[(0,r.jsx)("option",{value:"createdAt",children:"Created Date"}),(0,r.jsx)("option",{value:"updatedAt",children:"Updated Date"}),(0,r.jsx)("option",{value:"title",children:"Title"}),(0,r.jsx)("option",{value:"priority",children:"Priority"}),(0,r.jsx)("option",{value:"dueDate",children:"Due Date"})]})]}),(0,r.jsxs)("button",{onClick:()=>i("sortOrder","desc"===s.sortOrder?"asc":"desc"),className:"flex items-center space-x-1 px-3 py-1 text-sm border border-secondary-300 rounded-md hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500",title:"Sort ".concat("desc"===s.sortOrder?"ascending":"descending"),children:["desc"===s.sortOrder?(0,r.jsx)(l.A,{className:"w-4 h-4"}):(0,r.jsx)(c.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"desc"===s.sortOrder?"Desc":"Asc"})]}),d&&(0,r.jsxs)("button",{onClick:()=>{t({})},className:"flex items-center space-x-1 px-3 py-1 text-sm text-danger-600 border border-danger-300 rounded-md hover:bg-danger-50 focus:outline-none focus:ring-2 focus:ring-danger-500 focus:border-danger-500",children:[(0,r.jsx)(n.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Clear"})]}),d&&(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-secondary-600",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[Object.keys(s).length," filter",1!==Object.keys(s).length?"s":""," active"]})]})]})}var u=t(2731),m=t(7210),x=t(4186),h=t(9074),p=t(2115),f=t(2177);function g(e){let{task:s,onSubmit:t,onCancel:a,isLoading:i=!1}=e,[l,c]=(0,p.useState)(!1),o=!!s,{register:d,handleSubmit:u,formState:{errors:g},reset:y,watch:j}=(0,f.mN)({defaultValues:{title:(null==s?void 0:s.title)||"",description:(null==s?void 0:s.description)||"",priority:(null==s?void 0:s.priority)||"medium",status:(null==s?void 0:s.status)||"todo",dueDate:(null==s?void 0:s.dueDate)?new Date(s.dueDate).toISOString().split("T")[0]:""}});(0,p.useEffect)(()=>{s&&y({title:s.title,description:s.description,priority:s.priority,status:s.status,dueDate:s.dueDate?new Date(s.dueDate).toISOString().split("T")[0]:""})},[s,y]);let b=async e=>{c(!0);try{let s={title:e.title.trim(),description:e.description.trim(),priority:e.priority,status:e.status,dueDate:e.dueDate?new Date(e.dueDate).toISOString():null};await t(s)}catch(e){console.error("Form submission error:",e)}finally{c(!1)}},N=()=>{y(),a()};return(0,p.useEffect)(()=>{let e=e=>{"Escape"===e.key&&N()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[]),(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-secondary-200",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-secondary-900",children:o?"Edit Task":"Create New Task"}),(0,r.jsx)("button",{onClick:N,className:"p-2 text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200",children:(0,r.jsx)(n.A,{className:"w-5 h-5"})})]}),(0,r.jsxs)("form",{onSubmit:u(b),className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"title",className:"form-label",children:"Task Title *"}),(0,r.jsx)("input",{id:"title",type:"text",...d("title",{required:"Task title is required",minLength:{value:1,message:"Title must be at least 1 character"},maxLength:{value:200,message:"Title must be less than 200 characters"}}),className:"form-input ".concat(g.title?"border-danger-300 focus:border-danger-500 focus:ring-danger-500":""),placeholder:"Enter task title...",disabled:l||i}),g.title&&(0,r.jsx)("p",{className:"form-error",children:g.title.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"description",className:"form-label",children:"Description"}),(0,r.jsx)("textarea",{id:"description",rows:3,...d("description",{maxLength:{value:1e3,message:"Description must be less than 1000 characters"}}),className:"form-input ".concat(g.description?"border-danger-300 focus:border-danger-500 focus:ring-danger-500":""),placeholder:"Enter task description...",disabled:l||i}),g.description&&(0,r.jsx)("p",{className:"form-error",children:g.description.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"priority",className:"form-label flex items-center space-x-1",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Priority"})]}),(0,r.jsxs)("select",{id:"priority",...d("priority"),className:"form-input",disabled:l||i,children:[(0,r.jsx)("option",{value:"low",children:"Low"}),(0,r.jsx)("option",{value:"medium",children:"Medium"}),(0,r.jsx)("option",{value:"high",children:"High"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"status",className:"form-label flex items-center space-x-1",children:[(0,r.jsx)(x.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Status"})]}),(0,r.jsxs)("select",{id:"status",...d("status"),className:"form-input",disabled:l||i,children:[(0,r.jsx)("option",{value:"todo",children:"To Do"}),(0,r.jsx)("option",{value:"in-progress",children:"In Progress"}),(0,r.jsx)("option",{value:"completed",children:"Completed"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"dueDate",className:"form-label flex items-center space-x-1",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Due Date"})]}),(0,r.jsx)("input",{id:"dueDate",type:"date",...d("dueDate"),className:"form-input",disabled:l||i,min:new Date().toISOString().split("T")[0]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3 pt-4 border-t border-secondary-200",children:[(0,r.jsx)("button",{type:"button",onClick:N,className:"btn-outline",disabled:l||i,children:"Cancel"}),(0,r.jsxs)("button",{type:"submit",className:"btn-primary flex items-center space-x-2",disabled:l||i,children:[(l||i)&&(0,r.jsx)("div",{className:"spinner w-4 h-4"}),(0,r.jsx)("span",{children:l||i?o?"Updating...":"Creating...":o?"Update Task":"Create Task"})]})]})]})]})})}var y=t(2915),j=t(9428),b=t(6286),N=t(6287),v=t(2525),w=t(809);function k(e){let{isOpen:s,title:t,message:a,confirmText:i="Confirm",cancelText:l="Cancel",variant:c="danger",onConfirm:o,onCancel:d,isLoading:u=!1}=e;if((0,p.useEffect)(()=>{let e=e=>{"Escape"!==e.key||u||d()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,u,d]),!s)return null;let m={danger:{icon:"text-danger-600",button:"btn-danger"},warning:{icon:"text-warning-600",button:"btn-warning"},info:{icon:"text-primary-600",button:"btn-primary"}}[c];return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-xl max-w-md w-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-secondary-200",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-opacity-20 flex items-center justify-center ".concat("danger"===c?"bg-danger-100":"warning"===c?"bg-warning-100":"bg-primary-100"),children:(0,r.jsx)(w.A,{className:"w-5 h-5 ".concat(m.icon)})}),(0,r.jsx)("h2",{className:"text-lg font-semibold text-secondary-900",children:t})]}),!u&&(0,r.jsx)("button",{onClick:d,className:"p-2 text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200",children:(0,r.jsx)(n.A,{className:"w-5 h-5"})})]}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("p",{className:"text-secondary-600 leading-relaxed",children:a})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3 p-6 border-t border-secondary-200 bg-secondary-50 rounded-b-xl",children:[(0,r.jsx)("button",{onClick:d,disabled:u,className:"btn-outline",children:l}),(0,r.jsxs)("button",{onClick:o,disabled:u,className:"".concat(m.button," flex items-center space-x-2"),children:[u&&(0,r.jsx)("div",{className:"spinner w-4 h-4"}),(0,r.jsx)("span",{children:u?"Processing...":i})]})]})]})})}function A(e){let{tasks:s,onEdit:t,onDelete:n,onUpdate:i}=e,[l,c]=(0,p.useState)(null),[o,d]=(0,p.useState)(null),u=e=>{c(e),d(null)},f=e=>{t(e),d(null)},g=e=>{switch(e){case"high":return"text-danger-600 bg-danger-50";case"medium":return"text-warning-600 bg-warning-50";case"low":return"text-success-600 bg-success-50";default:return"text-secondary-600 bg-secondary-50"}},w=e=>{switch(e){case"completed":return(0,r.jsx)(y.A,{className:"w-5 h-5 text-success-600"});case"in-progress":return(0,r.jsx)(x.A,{className:"w-5 h-5 text-warning-600"});default:return(0,r.jsx)(j.A,{className:"w-5 h-5 text-secondary-400"})}},A=e=>{switch(e){case"completed":return"text-success-700 bg-success-50 border-success-200";case"in-progress":return"text-warning-700 bg-warning-50 border-warning-200";default:return"text-secondary-700 bg-secondary-50 border-secondary-200"}},S=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),C=e=>!!e&&new Date(e)<new Date;return 0===s.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-secondary-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(y.A,{className:"w-8 h-8 text-secondary-400"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-secondary-900 mb-2",children:"No tasks found"}),(0,r.jsx)("p",{className:"text-secondary-600",children:"Create your first task to get started with managing your work."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"divide-y divide-secondary-200",children:s.map(e=>(0,r.jsx)("div",{className:"p-4 hover:bg-secondary-50 transition-colors duration-200",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3 flex-1",children:[(0,r.jsx)("div",{className:"flex-shrink-0 mt-1",children:w(e.status)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-sm font-medium ".concat("completed"===e.status?"text-secondary-500 line-through":"text-secondary-900"),children:e.title}),e.description&&(0,r.jsx)("p",{className:"mt-1 text-sm ".concat("completed"===e.status?"text-secondary-400":"text-secondary-600"),children:e.description})]})}),(0,r.jsxs)("div",{className:"mt-3 flex items-center space-x-4 text-xs",children:[(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full font-medium ".concat(g(e.priority)),children:[(0,r.jsx)(m.A,{className:"w-3 h-3 mr-1"}),e.priority]}),(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full border font-medium ".concat(A(e.status)),children:e.status.replace("-"," ")}),e.dueDate&&(0,r.jsxs)("span",{className:"inline-flex items-center ".concat(C(e.dueDate)?"text-danger-600":"text-secondary-500"),children:[(0,r.jsx)(h.A,{className:"w-3 h-3 mr-1"}),S(e.dueDate),C(e.dueDate)&&(0,r.jsx)(a.A,{className:"w-3 h-3 ml-1"})]}),(0,r.jsxs)("span",{className:"text-secondary-400",children:["Created ",S(e.createdAt)]})]})]})]}),(0,r.jsxs)("div",{className:"relative flex-shrink-0 ml-4",children:[(0,r.jsx)("button",{onClick:()=>d(o===e.id?null:e.id),className:"p-1 rounded-md text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 transition-colors duration-200",children:(0,r.jsx)(b.A,{className:"w-4 h-4"})}),o===e.id&&(0,r.jsx)("div",{className:"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-secondary-200 z-10",children:(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("button",{onClick:()=>f(e),className:"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-200",children:[(0,r.jsx)(N.A,{className:"w-4 h-4 mr-3"}),"Edit Task"]}),(0,r.jsxs)("button",{onClick:()=>u(e.id),className:"flex items-center w-full px-4 py-2 text-sm text-danger-700 hover:bg-danger-50 transition-colors duration-200",children:[(0,r.jsx)(v.A,{className:"w-4 h-4 mr-3"}),"Delete Task"]})]})})]})]})},e.id))}),(0,r.jsx)(k,{isOpen:!!l,title:"Delete Task",message:"Are you sure you want to delete this task? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",onConfirm:()=>{l&&(n(l),c(null))},onCancel:()=>{c(null)},variant:"danger"}),o&&(0,r.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>d(null)})]})}var S=t(1284);function C(e){let{id:s,type:t,title:i,message:l,duration:c=5e3,onClose:o}=e,[d,u]=(0,p.useState)(!1),[m,x]=(0,p.useState)(!1);(0,p.useEffect)(()=>{let e=setTimeout(()=>u(!0),10);return()=>clearTimeout(e)},[]),(0,p.useEffect)(()=>{if(c>0){let e=setTimeout(()=>{h()},c);return()=>clearTimeout(e)}},[c]);let h=()=>{x(!0),setTimeout(()=>{o(s)},300)};return(0,r.jsx)("div",{className:"\n        transform transition-all duration-300 ease-in-out\n        ".concat(d&&!m?"translate-x-0 opacity-100":"translate-x-full opacity-0","\n        ").concat((()=>{switch(t){case"success":return"bg-success-50 border-success-200";case"error":return"bg-danger-50 border-danger-200";case"warning":return"bg-warning-50 border-warning-200";default:return"bg-primary-50 border-primary-200"}})(),"\n        border rounded-lg shadow-lg p-4 mb-3 max-w-sm w-full\n      "),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(t){case"success":return(0,r.jsx)(y.A,{className:"w-5 h-5 text-success-600"});case"error":return(0,r.jsx)(a.A,{className:"w-5 h-5 text-danger-600"});case"warning":return(0,r.jsx)(a.A,{className:"w-5 h-5 text-warning-600"});default:return(0,r.jsx)(S.A,{className:"w-5 h-5 text-primary-600"})}})()}),(0,r.jsxs)("div",{className:"ml-3 flex-1",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-secondary-900",children:i}),l&&(0,r.jsx)("p",{className:"mt-1 text-sm text-secondary-600",children:l})]}),(0,r.jsx)("button",{onClick:h,className:"ml-4 flex-shrink-0 p-1 rounded-md text-secondary-400 hover:text-secondary-600 transition-colors duration-200",children:(0,r.jsx)(n.A,{className:"w-4 h-4"})})]})})}function D(e){let{toasts:s,onClose:t}=e;return(0,r.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.map(e=>(0,r.jsx)(C,{...e,onClose:t},e.id))})}var T=t(6474),E=t(1007),U=t(9397),O=t(381),P=t(4835);function L(e){var s,t;let{user:a,onLogout:n}=e,[i,l]=(0,p.useState)(!1),c=(0,p.useRef)(null);(0,p.useEffect)(()=>{let e=e=>{c.current&&!c.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,p.useEffect)(()=>{let e=e=>{"Escape"===e.key&&l(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[]);let o=e=>e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2);return(0,r.jsxs)("div",{className:"relative",ref:c,children:[(0,r.jsxs)("button",{onClick:()=>l(!i),className:"flex items-center space-x-3 p-2 rounded-lg hover:bg-secondary-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white text-sm font-medium",children:o(a.name)}),(0,r.jsxs)("div",{className:"hidden md:block text-left",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-secondary-900",children:a.name}),(0,r.jsx)("p",{className:"text-xs text-secondary-600",children:a.email})]}),(0,r.jsx)(T.A,{className:"w-4 h-4 text-secondary-600 transition-transform duration-200 ".concat(i?"transform rotate-180":"")})]}),i&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-secondary-200 py-2 z-50",children:[(0,r.jsxs)("div",{className:"px-4 py-3 border-b border-secondary-200",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center text-white font-medium",children:o(a.name)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-secondary-900 truncate",children:a.name}),(0,r.jsx)("p",{className:"text-xs text-secondary-600 truncate",children:a.email}),a.username&&(0,r.jsxs)("p",{className:"text-xs text-secondary-500 truncate",children:["@",a.username]})]})]}),a.roles&&a.roles.length>0||a.groups&&a.groups.length>0?(0,r.jsxs)("div",{className:"mt-2 flex flex-wrap gap-1",children:[null==(s=a.roles)?void 0:s.map(e=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800",children:e},e)),null==(t=a.groups)?void 0:t.map(e=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-secondary-100 text-secondary-800",children:e},e))]}):null]}),(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("button",{onClick:()=>{l(!1),console.log("View profile clicked")},className:"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 transition-colors duration-200",children:[(0,r.jsx)(E.A,{className:"w-4 h-4 mr-3"}),"View Profile"]}),(0,r.jsxs)("button",{onClick:()=>{l(!1),console.log("View activity clicked")},className:"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 transition-colors duration-200",children:[(0,r.jsx)(U.A,{className:"w-4 h-4 mr-3"}),"Activity"]}),(0,r.jsxs)("button",{onClick:()=>{l(!1),console.log("Settings clicked")},className:"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 transition-colors duration-200",children:[(0,r.jsx)(O.A,{className:"w-4 h-4 mr-3"}),"Settings"]})]}),(0,r.jsx)("div",{className:"border-t border-secondary-200 py-1",children:(0,r.jsxs)("button",{onClick:()=>{l(!1),n()},className:"flex items-center w-full px-4 py-2 text-sm text-danger-700 hover:bg-danger-50 transition-colors duration-200",children:[(0,r.jsx)(P.A,{className:"w-4 h-4 mr-3"}),"Sign Out"]})})]})]})}var I=t(9323);class q extends Error{constructor(e,s,t){super(e),this.status=s,this.response=t,this.name="ApiClientError"}}class F{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t="".concat(this.baseUrl).concat(e),r={credentials:"include",headers:{"Content-Type":"application/json",Accept:"application/json",...s.headers},...s};try{let e=await fetch(t,r);if(401===e.status)throw(0,I.en)(),new q("Authentication required",401);let s=await e.json();if(!e.ok)throw new q(s.message||"HTTP ".concat(e.status),e.status,s);return s}catch(e){if(e instanceof q)throw e;throw console.error("API request failed:",e),new q("Network error or server unavailable",0,e)}}async getTasks(e){let s=new URLSearchParams;(null==e?void 0:e.status)&&s.append("status",e.status),(null==e?void 0:e.priority)&&s.append("priority",e.priority),(null==e?void 0:e.sortBy)&&s.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&s.append("sortOrder",e.sortOrder);let t=s.toString();return this.request("/tasks".concat(t?"?".concat(t):""))}async getTask(e){return this.request("/tasks/".concat(e))}async createTask(e){return this.request("/tasks",{method:"POST",body:JSON.stringify(e)})}async updateTask(e,s){return this.request("/tasks/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteTask(e){return this.request("/tasks/".concat(e),{method:"DELETE"})}async getTaskStats(){return this.request("/tasks/stats")}async getUserProfile(){return this.request("/user/profile")}async getUserPreferences(){return this.request("/user/preferences")}async updateUserPreferences(e){return this.request("/user/preferences",{method:"PUT",body:JSON.stringify(e)})}async getUserActivity(){return this.request("/user/activity")}async logoutUser(){return this.request("/user/logout",{method:"POST"})}async getUserPermissions(){return this.request("/user/permissions")}constructor(e="/choreo-apis"){this.baseUrl=e}}let _=new F,z={getAll:e=>_.getTasks(e),create:e=>_.createTask(e),update:(e,s)=>_.updateTask(e,s),delete:e=>_.deleteTask(e),getStats:()=>_.getTaskStats()},J=e=>e instanceof q,B=e=>J(e)||e instanceof Error?e.message:"An unexpected error occurred";var M=t(4616),V=t(4576),H=t(7924);function R(){let[e,s]=(0,p.useState)(null),{toasts:t,removeToast:n,success:l,error:c}=function(){let[e,s]=(0,p.useState)([]),t=e=>{let t=Math.random().toString(36).substr(2,9),a={...e,id:t,onClose:r};s(e=>[...e,a])},r=e=>{s(s=>s.filter(s=>s.id!==e))};return{toasts:e,addToast:t,removeToast:r,success:(e,s)=>{t({type:"success",title:e,message:s})},error:(e,s)=>{t({type:"error",title:e,message:s})},warning:(e,s)=>{t({type:"warning",title:e,message:s})},info:(e,s)=>{t({type:"info",title:e,message:s})}}}(),[o,m]=(0,p.useState)([]),[h,f]=(0,p.useState)(null),[j,b]=(0,p.useState)({}),[N,v]=(0,p.useState)(!0),[w,k]=(0,p.useState)(null),[S,C]=(0,p.useState)(!1),[T,E]=(0,p.useState)(null),[U,O]=(0,p.useState)("");(0,p.useEffect)(()=>{P()},[]),(0,p.useEffect)(()=>{e&&q()},[j,e]);let P=async()=>{try{v(!0),k(null);let e=await I.y1.checkAuthStatus();if(!e.isAuthenticated)return void I.y1.login("/dashboard");s(e.user),await Promise.all([q(),F()])}catch(e){console.error("Dashboard initialization error:",e),k(B(e))}finally{v(!1)}},q=async()=>{try{let e=await z.getAll(j);m(e.tasks)}catch(e){console.error("Error loading tasks:",e),k(B(e))}},F=async()=>{try{let e=await z.getStats();f(e.stats)}catch(e){console.error("Error loading task stats:",e)}},_=async e=>{try{let s=await z.create(e);m(e=>[s.task,...e]),C(!1),await F(),l("Task created","Your new task has been created successfully")}catch(e){console.error("Error creating task:",e),c("Failed to create task",B(e))}},J=async e=>{if(T)try{let s=await z.update(T.id,e);m(e=>e.map(e=>e.id===T.id?s.task:e)),E(null),await F(),l("Task updated","Your task has been updated successfully")}catch(e){console.error("Error updating task:",e),c("Failed to update task",B(e))}},R=async e=>{try{await z.delete(e),m(s=>s.filter(s=>s.id!==e)),await F()}catch(e){console.error("Error deleting task:",e),k(B(e))}},Y=o.filter(e=>e.title.toLowerCase().includes(U.toLowerCase())||e.description.toLowerCase().includes(U.toLowerCase()));return N?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-secondary-50",children:(0,r.jsx)(u.A,{size:"large"})}):(0,r.jsxs)("div",{className:"min-h-screen bg-secondary-50",children:[(0,r.jsx)(D,{toasts:t,onClose:n}),(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-secondary-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)(y.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("h1",{className:"text-xl font-bold text-secondary-900",children:"Task Management"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:()=>C(!0),className:"btn-primary flex items-center space-x-2",children:[(0,r.jsx)(M.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"New Task"})]}),e&&(0,r.jsx)(L,{user:e,onLogout:()=>{I.y1.logout()}})]})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[w&&(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)(i,{message:w,onDismiss:()=>k(null)})}),h&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"card p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-secondary-600",children:"Total Tasks"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-secondary-900",children:h.total})]}),(0,r.jsx)(V.A,{className:"w-8 h-8 text-primary-600"})]})}),(0,r.jsx)("div",{className:"card p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-secondary-600",children:"In Progress"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-warning-600",children:h.byStatus["in-progress"]})]}),(0,r.jsx)(x.A,{className:"w-8 h-8 text-warning-600"})]})}),(0,r.jsx)("div",{className:"card p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-secondary-600",children:"Completed"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-success-600",children:h.byStatus.completed})]}),(0,r.jsx)(y.A,{className:"w-8 h-8 text-success-600"})]})}),(0,r.jsx)("div",{className:"card p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-secondary-600",children:"Overdue"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-danger-600",children:h.overdue})]}),(0,r.jsx)(a.A,{className:"w-8 h-8 text-danger-600"})]})})]}),(0,r.jsx)("div",{className:"card mb-6",children:(0,r.jsx)("div",{className:"card-body",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,r.jsx)("div",{className:"flex-1 max-w-md",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(H.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search tasks...",value:U,onChange:e=>O(e.target.value),className:"form-input pl-10"})]})}),h&&(0,r.jsx)(d,{filters:j,onFiltersChange:e=>{b(e)},taskStats:h})]})})}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("div",{className:"card-header",children:(0,r.jsxs)("h2",{className:"text-lg font-semibold text-secondary-900",children:["Your Tasks (",Y.length,")"]})}),(0,r.jsx)("div",{className:"card-body p-0",children:(0,r.jsx)(A,{tasks:Y,onEdit:e=>{E(e),C(!0)},onDelete:R,onUpdate:q})})]})]}),S&&(0,r.jsx)(g,{task:T,onSubmit:T?J:_,onCancel:()=>{C(!1),E(null)}})]})}},8615:(e,s,t)=>{Promise.resolve().then(t.bind(t,4337))},9323:(e,s,t)=>{"use strict";t.d(s,{en:()=>i,y1:()=>n});var r=t(7383);class a{static getInstance(){return a.instance||(a.instance=new a),a.instance}login(e){let s="/auth/login",t=e?"".concat(s,"?redirect=").concat(encodeURIComponent(e)):s;window.location.href=t}logout(){let e=r.A.get("session_hint");this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),window.location.href=e?"/auth/logout?session_hint=".concat(e):"/auth/logout"}getUserInfoFromCookie(){try{let e=r.A.get("userinfo");if(!e)return null;let s=JSON.parse(atob(e));return r.A.remove("userinfo",{path:"/"}),this.user={id:s.sub,email:s.email,name:s.name||"".concat(s.given_name||""," ").concat(s.family_name||"").trim(),username:s.preferred_username,groups:s.groups||[],roles:s.roles||[],profileComplete:!!(s.name&&s.email)},this.isAuthenticated=!0,this.storeUserData(this.user),this.user}catch(e){return console.error("Error parsing user info cookie:",e),null}}async checkAuthStatus(){try{let e=await fetch("/auth/userinfo",{method:"GET",credentials:"include",headers:{Accept:"application/json"}});if(e.ok){let s=await e.json();return this.user={id:s.sub,email:s.email,name:s.name||"".concat(s.given_name||""," ").concat(s.family_name||"").trim(),username:s.preferred_username,groups:s.groups||[],roles:s.roles||[],profileComplete:!!(s.name&&s.email)},this.isAuthenticated=!0,this.storeUserData(this.user),{isAuthenticated:!0,user:this.user}}if(401===e.status)return this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),{isAuthenticated:!1,user:null};throw Error("Auth check failed with status: ".concat(e.status))}catch(s){console.error("Error checking auth status:",s);let e=this.getStoredUserData();if(e)return this.user=e,this.isAuthenticated=!0,{isAuthenticated:!0,user:e};return{isAuthenticated:!1,user:null}}}getCurrentUser(){if(this.user)return this.user;let e=this.getStoredUserData();return e?(this.user=e,this.isAuthenticated=!0,e):null}getIsAuthenticated(){return this.isAuthenticated||!!this.getStoredUserData()}handleSessionExpiry(){console.log("Session expired, redirecting to login..."),this.user=null,this.isAuthenticated=!1,this.clearStoredUserData(),this.login(window.location.pathname)}storeUserData(e){try{localStorage.setItem("user",JSON.stringify(e)),localStorage.setItem("lastLogin",new Date().toISOString())}catch(e){console.error("Error storing user data:",e)}}getStoredUserData(){try{let e=localStorage.getItem("user");if(e)return JSON.parse(e)}catch(e){console.error("Error retrieving stored user data:",e)}return null}clearStoredUserData(){try{localStorage.removeItem("user"),localStorage.removeItem("lastLogin")}catch(e){console.error("Error clearing stored user data:",e)}}constructor(){this.user=null,this.isAuthenticated=!1}}let n=a.getInstance(),i=()=>n.handleSessionExpiry()}},e=>{var s=s=>e(e.s=s);e.O(0,[234,441,684,358],()=>s(8615)),_N_E=e.O()}]);