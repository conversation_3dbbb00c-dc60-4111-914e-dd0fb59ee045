[{"C:\\Choreo\\frontend\\src\\app\\auth\\error\\page.tsx": "1", "C:\\Choreo\\frontend\\src\\app\\dashboard\\layout.tsx": "2", "C:\\Choreo\\frontend\\src\\app\\dashboard\\page.tsx": "3", "C:\\Choreo\\frontend\\src\\app\\layout.tsx": "4", "C:\\Choreo\\frontend\\src\\app\\page.tsx": "5", "C:\\Choreo\\frontend\\src\\components\\ConfirmDialog.tsx": "6", "C:\\Choreo\\frontend\\src\\components\\ErrorMessage.tsx": "7", "C:\\Choreo\\frontend\\src\\components\\FilterBar.tsx": "8", "C:\\Choreo\\frontend\\src\\components\\LoadingSpinner.tsx": "9", "C:\\Choreo\\frontend\\src\\components\\TaskForm.tsx": "10", "C:\\Choreo\\frontend\\src\\components\\Toast.tsx": "11", "C:\\Choreo\\frontend\\src\\components\\UserProfile.tsx": "12", "C:\\Choreo\\frontend\\src\\hooks\\useAuth.ts": "13", "C:\\Choreo\\frontend\\src\\hooks\\useTasks.ts": "14", "C:\\Choreo\\frontend\\src\\lib\\api.ts": "15", "C:\\Choreo\\frontend\\src\\lib\\auth.ts": "16", "C:\\Choreo\\frontend\\src\\lib\\utils.ts": "17", "C:\\Choreo\\frontend\\src\\types\\index.ts": "18", "C:\\Choreo\\frontend\\src\\components\\TaskList.tsx": "19"}, {"size": 5246, "mtime": 1751998134846, "results": "20", "hashOfConfig": "21"}, {"size": 1797, "mtime": 1751991669933, "results": "22", "hashOfConfig": "21"}, {"size": 10272, "mtime": 1751997390903, "results": "23", "hashOfConfig": "21"}, {"size": 1341, "mtime": 1751998075977, "results": "24", "hashOfConfig": "21"}, {"size": 9143, "mtime": 1751997068604, "results": "25", "hashOfConfig": "21"}, {"size": 3501, "mtime": 1751992639742, "results": "26", "hashOfConfig": "21"}, {"size": 1347, "mtime": 1751991644450, "results": "27", "hashOfConfig": "21"}, {"size": 4700, "mtime": 1751991558021, "results": "28", "hashOfConfig": "21"}, {"size": 594, "mtime": 1751991621773, "results": "29", "hashOfConfig": "21"}, {"size": 7999, "mtime": 1751997421076, "results": "30", "hashOfConfig": "21"}, {"size": 4406, "mtime": 1751997456147, "results": "31", "hashOfConfig": "21"}, {"size": 6344, "mtime": 1751991598087, "results": "32", "hashOfConfig": "21"}, {"size": 2049, "mtime": 1751992401000, "results": "33", "hashOfConfig": "21"}, {"size": 3856, "mtime": 1751997484120, "results": "34", "hashOfConfig": "21"}, {"size": 6360, "mtime": 1751991387055, "results": "35", "hashOfConfig": "21"}, {"size": 6656, "mtime": 1751991351392, "results": "36", "hashOfConfig": "21"}, {"size": 6649, "mtime": 1751992600164, "results": "37", "hashOfConfig": "21"}, {"size": 3531, "mtime": 1751991309239, "results": "38", "hashOfConfig": "21"}, {"size": 8427, "mtime": 1751996827454, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4tng4q", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Choreo\\frontend\\src\\app\\auth\\error\\page.tsx", [], [], "C:\\Choreo\\frontend\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Choreo\\frontend\\src\\app\\dashboard\\page.tsx", [], ["97", "98"], "C:\\Choreo\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Choreo\\frontend\\src\\app\\page.tsx", [], [], "C:\\Choreo\\frontend\\src\\components\\ConfirmDialog.tsx", [], [], "C:\\Choreo\\frontend\\src\\components\\ErrorMessage.tsx", [], [], "C:\\Choreo\\frontend\\src\\components\\FilterBar.tsx", [], [], "C:\\Choreo\\frontend\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Choreo\\frontend\\src\\components\\TaskForm.tsx", [], ["99"], "C:\\Choreo\\frontend\\src\\components\\Toast.tsx", [], ["100"], "C:\\Choreo\\frontend\\src\\components\\UserProfile.tsx", [], [], "C:\\Choreo\\frontend\\src\\hooks\\useAuth.ts", [], [], "C:\\Choreo\\frontend\\src\\hooks\\useTasks.ts", [], ["101", "102"], "C:\\Choreo\\frontend\\src\\lib\\api.ts", [], [], "C:\\Choreo\\frontend\\src\\lib\\auth.ts", [], [], "C:\\Choreo\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Choreo\\frontend\\src\\types\\index.ts", [], [], "C:\\Choreo\\frontend\\src\\components\\TaskList.tsx", [], [], {"ruleId": "103", "severity": 1, "message": "104", "line": 32, "column": 6, "nodeType": "105", "endLine": 32, "endColumn": 8, "suggestions": "106", "suppressions": "107"}, {"ruleId": "103", "severity": 1, "message": "108", "line": 39, "column": 6, "nodeType": "105", "endLine": 39, "endColumn": 21, "suggestions": "109", "suppressions": "110"}, {"ruleId": "103", "severity": 1, "message": "111", "line": 91, "column": 6, "nodeType": "105", "endLine": 91, "endColumn": 8, "suggestions": "112", "suppressions": "113"}, {"ruleId": "103", "severity": 1, "message": "114", "line": 39, "column": 6, "nodeType": "105", "endLine": 39, "endColumn": 16, "suggestions": "115", "suppressions": "116"}, {"ruleId": "103", "severity": 1, "message": "108", "line": 34, "column": 6, "nodeType": "105", "endLine": 34, "endColumn": 15, "suggestions": "117", "suppressions": "118"}, {"ruleId": "103", "severity": 1, "message": "119", "line": 39, "column": 6, "nodeType": "105", "endLine": 39, "endColumn": 8, "suggestions": "120", "suppressions": "121"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeDashboard'. Either include it or remove the dependency array.", "ArrayExpression", ["122"], ["123"], "React Hook useEffect has a missing dependency: 'loadTasks'. Either include it or remove the dependency array.", ["124"], ["125"], "React Hook useEffect has a missing dependency: 'handleCancel'. Either include it or remove the dependency array.", ["126"], ["127"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["128"], ["129"], ["130"], ["131"], "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["132"], ["133"], {"desc": "134", "fix": "135"}, {"kind": "136", "justification": "137"}, {"desc": "138", "fix": "139"}, {"kind": "136", "justification": "137"}, {"desc": "140", "fix": "141"}, {"kind": "136", "justification": "137"}, {"desc": "142", "fix": "143"}, {"kind": "136", "justification": "137"}, {"desc": "144", "fix": "145"}, {"kind": "136", "justification": "137"}, {"desc": "146", "fix": "147"}, {"kind": "136", "justification": "137"}, "Update the dependencies array to be: [initializeDashboard]", {"range": "148", "text": "149"}, "directive", "", "Update the dependencies array to be: [filters, loadTasks, user]", {"range": "150", "text": "151"}, "Update the dependencies array to be: [handleCancel]", {"range": "152", "text": "153"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "154", "text": "155"}, "Update the dependencies array to be: [filters, loadTasks]", {"range": "156", "text": "157"}, "Update the dependencies array to be: [loadInitialData]", {"range": "158", "text": "159"}, [1413, 1415], "[initializeDashboard]", [1570, 1585], "[filters, loadTasks, user]", [2341, 2343], "[handleCancel]", [857, 867], "[duration, handleClose]", [1119, 1128], "[filters, loadTasks]", [1252, 1254], "[loadInitialData]"]