(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1105:(e,s,a)=>{Promise.resolve().then(a.bind(a,3792))},3792:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var t=a(5155),c=a(2915),r=a(2138),n=a(9099),l=a(3786),i=a(7580),x=a(4186),d=a(6874),m=a.n(d),o=a(2115);function h(){let[e,s]=(0,o.useState)(!0);return((0,o.useEffect)(()=>{let e=setTimeout(()=>s(!1),500);return()=>clearTimeout(e)},[]),e)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-100",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-secondary-600",children:"Loading..."})]})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100",children:[(0,t.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b border-secondary-200 sticky top-0 z-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)(c.A,{className:"w-5 h-5 text-white"})}),(0,t.jsx)("h1",{className:"text-xl font-bold text-secondary-900",children:"Task Management"})]}),(0,t.jsx)("nav",{className:"flex items-center space-x-4",children:(0,t.jsxs)(m(),{href:"/dashboard",className:"btn-primary flex items-center space-x-2",children:[(0,t.jsx)("span",{children:"Get Started"}),(0,t.jsx)(r.A,{className:"w-4 h-4"})]})})]})})}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsxs)("h2",{className:"text-4xl md:text-6xl font-bold text-secondary-900 mb-6 text-balance",children:["Manage Your Tasks with",(0,t.jsx)("span",{className:"text-primary-600 block",children:"WSO2 Choreo"})]}),(0,t.jsx)("p",{className:"text-xl text-secondary-600 mb-8 max-w-3xl mx-auto text-balance",children:"A comprehensive full-stack sample application demonstrating modern web development with Next.js frontend and Node.js backend, all deployed on WSO2 Choreo platform."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(m(),{href:"/dashboard",className:"btn-primary text-lg px-8 py-3",children:"Start Managing Tasks"}),(0,t.jsxs)("a",{href:"https://github.com/your-org/choreo-fullstack-sample",target:"_blank",rel:"noopener noreferrer",className:"btn-outline text-lg px-8 py-3 flex items-center justify-center space-x-2",children:[(0,t.jsx)(n.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"View Source"}),(0,t.jsx)(l.A,{className:"w-4 h-4"})]})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 mb-16",children:[(0,t.jsxs)("div",{className:"card p-8 text-center hover:shadow-medium transition-shadow duration-300",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(c.A,{className:"w-6 h-6 text-primary-600"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-secondary-900 mb-3",children:"Task Management"}),(0,t.jsx)("p",{className:"text-secondary-600",children:"Create, update, and organize your tasks with an intuitive interface. Set priorities, due dates, and track progress effortlessly."})]}),(0,t.jsxs)("div",{className:"card p-8 text-center hover:shadow-medium transition-shadow duration-300",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-success-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(i.A,{className:"w-6 h-6 text-success-600"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-secondary-900 mb-3",children:"User Authentication"}),(0,t.jsx)("p",{className:"text-secondary-600",children:"Secure authentication powered by Choreo's managed authentication system. No complex setup required - just focus on your application logic."})]}),(0,t.jsxs)("div",{className:"card p-8 text-center hover:shadow-medium transition-shadow duration-300",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-warning-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(x.A,{className:"w-6 h-6 text-warning-600"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-secondary-900 mb-3",children:"Real-time Updates"}),(0,t.jsx)("p",{className:"text-secondary-600",children:"Experience seamless real-time updates with optimistic UI patterns and efficient API communication between frontend and backend."})]})]}),(0,t.jsxs)("div",{className:"card p-8 mb-16",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-secondary-900 mb-6 text-center",children:"Built with Modern Technologies"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-secondary-900 mb-4",children:"Frontend"}),(0,t.jsxs)("ul",{className:"space-y-2 text-secondary-600",children:[(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 text-success-600"}),(0,t.jsx)("span",{children:"Next.js 14 with App Router"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 text-success-600"}),(0,t.jsx)("span",{children:"TypeScript for type safety"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 text-success-600"}),(0,t.jsx)("span",{children:"Tailwind CSS for styling"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 text-success-600"}),(0,t.jsx)("span",{children:"Choreo managed authentication"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-secondary-900 mb-4",children:"Backend"}),(0,t.jsxs)("ul",{className:"space-y-2 text-secondary-600",children:[(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 text-success-600"}),(0,t.jsx)("span",{children:"Node.js with Express.js"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 text-success-600"}),(0,t.jsx)("span",{children:"RESTful API with OpenAPI spec"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 text-success-600"}),(0,t.jsx)("span",{children:"JWT authentication integration"})]}),(0,t.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 text-success-600"}),(0,t.jsx)("span",{children:"Health monitoring endpoints"})]})]})]})]})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-secondary-900 mb-4",children:"Ready to Get Started?"}),(0,t.jsx)("p",{className:"text-lg text-secondary-600 mb-8 max-w-2xl mx-auto",children:"Experience the power of full-stack development on WSO2 Choreo. Sign in to start managing your tasks and explore the application features."}),(0,t.jsxs)(m(),{href:"/dashboard",className:"btn-primary text-lg px-8 py-3 inline-flex items-center space-x-2",children:[(0,t.jsx)("span",{children:"Launch Application"}),(0,t.jsx)(r.A,{className:"w-5 h-5"})]})]})]}),(0,t.jsx)("footer",{className:"bg-white border-t border-secondary-200 mt-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsx)("div",{className:"text-center text-secondary-600",children:(0,t.jsxs)("p",{children:["Built with ❤️ for the WSO2 Choreo community.",(0,t.jsx)("a",{href:"https://wso2.com/choreo/",target:"_blank",rel:"noopener noreferrer",className:"text-primary-600 hover:text-primary-700 ml-1",children:"Learn more about Choreo"})]})})})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[528,441,684,358],()=>s(1105)),_N_E=e.O()}]);