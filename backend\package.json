{"name": "choreo-task-management-api", "version": "1.0.0", "description": "Task Management API service for WSO2 Choreo full-stack sample", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["choreo", "nodejs", "express", "api", "task-management", "rest"], "author": "WSO2 Choreo Team", "license": "Apache-2.0", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "uuid": "^9.0.1", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.4.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/choreo-fullstack-sample.git", "directory": "backend"}, "bugs": {"url": "https://github.com/your-org/choreo-fullstack-sample/issues"}, "homepage": "https://github.com/your-org/choreo-fullstack-sample#readme"}