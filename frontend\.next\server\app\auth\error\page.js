(()=>{var e={};e.id=4,e.ids=[4],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},931:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=t(5239),n=t(8088),a=t(8170),i=t.n(a),o=t(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["auth",{children:["error",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9188)),"C:\\Choreo\\frontend\\src\\app\\auth\\error\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Choreo\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Choreo\\frontend\\src\\app\\auth\\error\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/error/page",pathname:"/auth/error",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},971:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},1135:()=>{},3017:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3227:(e,r,t)=>{Promise.resolve().then(t.bind(t,9188))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3851:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(8962).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>i,viewport:()=>o});var s=t(7413),n=t(5041),a=t.n(n);t(1135);let i={title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",keywords:["task management","choreo","nextjs","wso2","productivity"],authors:[{name:"WSO2 Choreo Team"}],robots:"index, follow",openGraph:{title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"Task Management - Choreo Full-Stack Sample",description:"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo"}},o={width:"device-width",initialScale:1};function l({children:e}){return(0,s.jsx)("html",{lang:"en",className:"h-full",children:(0,s.jsx)("body",{className:`${a().className} h-full`,children:(0,s.jsx)("div",{id:"root",className:"h-full",children:e})})})}},6189:(e,r,t)=>{"use strict";var s=t(5773);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},6569:()=>{},7763:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},7767:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(687),n=t(3851),a=t(8962);let i=(0,a.A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),o=(0,a.A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);var l=t(6189),d=t(3210);function c(){(0,l.useSearchParams)();let e=(0,l.useRouter)(),[r,t]=(0,d.useState)(null),[a,c]=(0,d.useState)(null);return(0,s.jsx)("div",{className:"min-h-screen bg-secondary-50 flex items-center justify-center px-4",children:(0,s.jsx)("div",{className:"max-w-md w-full",children:(0,s.jsxs)("div",{className:"card p-8 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-danger-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)(n.A,{className:"w-8 h-8 text-danger-600"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-secondary-900 mb-4",children:(e=>{switch(e){case"access_denied":return"Access Denied";case"invalid_request":return"Invalid Request";case"server_error":return"Server Error";case"temporarily_unavailable":return"Service Temporarily Unavailable";default:return"Authentication Error"}})(r)}),(0,s.jsx)("p",{className:"text-secondary-600 mb-8",children:((e,r)=>{if(r)return r;switch(e){case"access_denied":return"You have denied access to the application or do not have the required permissions.";case"invalid_request":return"The authentication request was invalid or malformed.";case"server_error":return"An internal server error occurred during authentication.";case"temporarily_unavailable":return"The authentication service is temporarily unavailable. Please try again later.";default:return"An unexpected error occurred during the authentication process."}})(r,a)}),r&&(0,s.jsxs)("div",{className:"bg-secondary-100 rounded-lg p-4 mb-6 text-left",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-secondary-900 mb-2",children:"Error Details:"}),(0,s.jsxs)("div",{className:"text-sm text-secondary-600 space-y-1",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Code:"})," ",r]}),a&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Message:"})," ",a]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Time:"})," ",new Date().toLocaleString()]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("button",{onClick:()=>{window.location.href="/auth/login"},className:"btn-primary w-full flex items-center justify-center space-x-2",children:[(0,s.jsx)(i,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Try Again"})]}),(0,s.jsxs)("button",{onClick:()=>{e.push("/")},className:"btn-outline w-full flex items-center justify-center space-x-2",children:[(0,s.jsx)(o,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Go to Home"})]})]}),(0,s.jsx)("div",{className:"mt-8 pt-6 border-t border-secondary-200",children:(0,s.jsx)("p",{className:"text-sm text-secondary-500",children:"If this problem persists, please contact support or try again later."})})]})})})}function u(){return(0,s.jsx)(d.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen bg-secondary-50 flex items-center justify-center px-4",children:(0,s.jsx)("div",{className:"max-w-md w-full",children:(0,s.jsxs)("div",{className:"card p-8 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)(n.A,{className:"w-8 h-8 text-secondary-600"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-secondary-900 mb-4",children:"Loading..."}),(0,s.jsx)("p",{className:"text-secondary-600",children:"Please wait while we load the error details."})]})})}),children:(0,s.jsx)(c,{})})}},8962:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(3210),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,r)=>{let t=(0,s.forwardRef)(({color:t="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:d="",children:c,...u},h)=>(0,s.createElement)("svg",{ref:h,...n,width:i,height:i,stroke:t,strokeWidth:l?24*Number(o)/Number(i):o,className:["lucide",`lucide-${a(e)}`,d].join(" "),...u},[...r.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(c)?c:[c]]));return t.displayName=`${e}`,t}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9188:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Choreo\\\\frontend\\\\src\\\\app\\\\auth\\\\error\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Choreo\\frontend\\src\\app\\auth\\error\\page.tsx","default")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9667:(e,r,t)=>{Promise.resolve().then(t.bind(t,7767))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[945],()=>t(931));module.exports=s})();