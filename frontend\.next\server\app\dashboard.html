<!DOCTYPE html><html lang="en" class="h-full"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/a035fee548feed9f.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-6f5cbf4814336177.js"/><script src="/_next/static/chunks/4bd1b696-eac4626368f1c4d0.js" async=""></script><script src="/_next/static/chunks/684-8f4d465db0ba7714.js" async=""></script><script src="/_next/static/chunks/main-app-32109416d021e512.js" async=""></script><script src="/_next/static/chunks/app/dashboard/layout-9669131c6bfe9f1a.js" async=""></script><script src="/_next/static/chunks/234-50ff2c684ae04b06.js" async=""></script><script src="/_next/static/chunks/app/dashboard/page-6611730509ae8492.js" async=""></script><title>Task Management - Choreo Full-Stack Sample</title><meta name="description" content="A comprehensive task management application built with Next.js and deployed on WSO2 Choreo"/><meta name="author" content="WSO2 Choreo Team"/><meta name="keywords" content="task management,choreo,nextjs,wso2,productivity"/><meta name="robots" content="index, follow"/><meta property="og:title" content="Task Management - Choreo Full-Stack Sample"/><meta property="og:description" content="A comprehensive task management application built with Next.js and deployed on WSO2 Choreo"/><meta property="og:locale" content="en_US"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="Task Management - Choreo Full-Stack Sample"/><meta name="twitter:description" content="A comprehensive task management application built with Next.js and deployed on WSO2 Choreo"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c h-full"><div hidden=""><!--$--><!--/$--></div><div id="root" class="h-full"><div class="min-h-screen flex items-center justify-center bg-secondary-50"><div class="flex flex-col items-center justify-center "><div class="spinner w-8 h-8"></div><p class="mt-2 text-sm text-secondary-600">Checking authentication...</p></div></div></div><script src="/_next/static/chunks/webpack-6f5cbf4814336177.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[4970,[],\"ClientSegmentRoot\"]\n5:I[388,[\"954\",\"static/chunks/app/dashboard/layout-9669131c6bfe9f1a.js\"],\"default\"]\n7:I[894,[],\"ClientPageRoot\"]\n8:I[4337,[\"234\",\"static/chunks/234-50ff2c684ae04b06.js\",\"105\",\"static/chunks/app/dashboard/page-6611730509ae8492.js\"],\"default\"]\nb:I[9665,[],\"OutletBoundary\"]\ne:I[4911,[],\"AsyncMetadataOutlet\"]\n10:I[9665,[],\"ViewportBoundary\"]\n12:I[9665,[],\"MetadataBoundary\"]\n14:I[6614,[],\"\"]\n:HL[\"/_next/static/css/a035fee548feed9f.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"vGfz-3IqR1cE_1-lR1Oay\",\"p\":\"\",\"c\":[\"\",\"dashboard\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"dashboard\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/a035fee548feed9f.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"h-full\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c h-full\",\"children\":[\"$\",\"div\",null,{\"id\":\"root\",\"className\":\"h-full\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"dashboard\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"slots\":{\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]},\"params\":{},\"promise\":\"$@6\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L7\",null,{\"Component\":\"$8\",\"searchParams\":{},\"params\":\"$0:f:0:1:2:children:1:props:children:1:props:params\",\"promises\":[\"$@9\",\"$@a\"]}],null,[\"$\",\"$Lb\",null,{\"children\":[\"$Lc\",\"$Ld\",[\"$\",\"$Le\",null,{\"promise\":\"$@f\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"PF2yqNRMHVSi3oW_9zPLQv\",{\"children\":[[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],null]}],[\"$\",\"$L12\",null,{\"children\":\"$L13\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$14\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"15:\"$Sreact.suspense\"\n16:I[4911,[],\"AsyncMetadata\"]\n6:{}\n9:{}\na:{}\n13:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$15\",null,{\"fallback\":null,\"children\":[\"$\",\"$L16\",null,{\"promise\":\"$@17\"}]}]}]\n"])</script><script>self.__next_f.push([1,"d:null\n"])</script><script>self.__next_f.push([1,"11:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nc:null\n"])</script><script>self.__next_f.push([1,"f:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Task Management - Choreo Full-Stack Sample\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"WSO2 Choreo Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"task management,choreo,nextjs,wso2,productivity\"}],[\"$\",\"meta\",\"4\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:title\",\"content\":\"Task Management - Choreo Full-Stack Sample\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:description\",\"content\":\"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"9\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"10\",{\"name\":\"twitter:title\",\"content\":\"Task Management - Choreo Full-Stack Sample\"}],[\"$\",\"meta\",\"11\",{\"name\":\"twitter:description\",\"content\":\"A comprehensive task management application built with Next.js and deployed on WSO2 Choreo\"}]],\"error\":null,\"digest\":\"$undefined\"}\n17:{\"metadata\":\"$f:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>